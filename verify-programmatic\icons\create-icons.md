# Icon Creation Guide

This directory should contain the following icon files for the AI Email Reply Assistant:

## Required Icons

### Extension Icons
- `icon-16.png` - 16x16 pixels (toolbar icon)
- `icon-32.png` - 32x32 pixels (addon manager)
- `icon-48.png` - 48x48 pixels (addon manager, notifications)
- `icon-64.png` - 64x64 pixels (addon manager)
- `icon-128.png` - 128x128 pixels (addon manager, high DPI)

## Icon Design Guidelines

### Visual Elements
- **Primary Symbol**: AI/Robot icon or brain symbol
- **Secondary Element**: Email/envelope symbol
- **Color Scheme**: 
  - Primary: Blue (#007bff) - represents technology/AI
  - Secondary: Green (#28a745) - represents success/assistance
  - Accent: White/Light gray for contrast

### Design Specifications
- **Style**: Modern, flat design with subtle gradients
- **Format**: PNG with transparency
- **Background**: Transparent or subtle gradient
- **Contrast**: High contrast for visibility in different themes

## Icon Creation Methods

### Method 1: Using Design Software
1. **Adobe Illustrator/Photoshop**:
   - Create vector design at high resolution
   - Export at different sizes
   - Ensure crisp edges at all sizes

2. **GIMP (Free Alternative)**:
   - Create design at 128x128
   - Scale down for smaller sizes
   - Use anti-aliasing for smooth edges

3. **Canva/Figma (Online)**:
   - Use AI/technology templates
   - Customize colors and elements
   - Export at required sizes

### Method 2: Using Icon Generators
1. **Favicon.io**:
   - Generate from text or image
   - Automatically creates multiple sizes
   - Download PNG pack

2. **IconKitchen**:
   - Upload base design
   - Generates all required sizes
   - Optimizes for different platforms

### Method 3: Using AI Tools
1. **DALL-E/Midjourney**:
   - Prompt: "Simple flat icon of AI robot helping with email, blue and green colors, transparent background"
   - Generate multiple variations
   - Edit and resize as needed

## Temporary Placeholder Creation

Until custom icons are created, you can use these CSS-based placeholders:

### CSS Icon (for development)
```css
.ai-icon {
  width: 16px;
  height: 16px;
  background: linear-gradient(45deg, #007bff, #28a745);
  border-radius: 3px;
  position: relative;
}

.ai-icon::before {
  content: "🤖";
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  font-size: 10px;
}
```

### SVG Icon Template
```svg
<svg width="48" height="48" viewBox="0 0 48 48" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <linearGradient id="grad1" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#007bff;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#28a745;stop-opacity:1" />
    </linearGradient>
  </defs>
  
  <!-- Background circle -->
  <circle cx="24" cy="24" r="20" fill="url(#grad1)" opacity="0.9"/>
  
  <!-- AI/Robot head -->
  <rect x="16" y="14" width="16" height="12" rx="2" fill="white" opacity="0.9"/>
  
  <!-- Eyes -->
  <circle cx="19" cy="18" r="1.5" fill="#007bff"/>
  <circle cx="29" cy="18" r="1.5" fill="#007bff"/>
  
  <!-- Mouth -->
  <rect x="20" y="22" width="8" height="1" rx="0.5" fill="#007bff"/>
  
  <!-- Email symbol -->
  <rect x="18" y="30" width="12" height="8" rx="1" fill="white" opacity="0.8"/>
  <path d="M18 30 L24 34 L30 30" stroke="#007bff" stroke-width="1" fill="none"/>
</svg>
```

## Implementation Steps

1. **Create Icons**: Use one of the methods above to create the required icon files
2. **Save Files**: Place all PNG files in the `icons/` directory
3. **Update Manifest**: Ensure `manifest.json` references the correct icon paths
4. **Test Icons**: Install the addon and verify icons display correctly
5. **Optimize**: Compress PNG files for smaller file sizes

## Icon Checklist

- [ ] icon-16.png created and optimized
- [ ] icon-32.png created and optimized  
- [ ] icon-48.png created and optimized
- [ ] icon-64.png created and optimized
- [ ] icon-128.png created and optimized
- [ ] Icons display correctly in Thunderbird toolbar
- [ ] Icons display correctly in addon manager
- [ ] Icons work with both light and dark themes
- [ ] File sizes are optimized (< 10KB each)

## Notes

- Icons should be recognizable at small sizes (16x16)
- Test icons with both light and dark Thunderbird themes
- Consider creating separate icons for different themes if needed
- Keep file sizes small for faster loading
- Use consistent visual style across all sizes

---

**Current Status**: Placeholder icons needed - please create actual icon files using the guidelines above.
