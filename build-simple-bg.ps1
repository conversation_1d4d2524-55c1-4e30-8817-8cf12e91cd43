#!/usr/bin/env pwsh

$buildDir = 'simple-bg-build'
$xpiName = 'ai-email-reply-simple-bg.xpi'

Write-Host "Building simple background XPI..."

# Clean up
if (Test-Path $buildDir) { Remove-Item $buildDir -Recurse -Force }
if (Test-Path $xpiName) { Remove-Item $xpiName -Force }

# Create build directory
New-Item -ItemType Directory -Path $buildDir | Out-Null

# Copy files
Copy-Item 'manifest.json' -Destination $buildDir
Copy-Item 'background-simple.js' -Destination $buildDir
Copy-Item 'popup' -Destination $buildDir -Recurse

Write-Host "Files copied to build directory"

# Create ZIP
Push-Location $buildDir
Compress-Archive -Path * -DestinationPath '../temp-simple-bg.zip' -CompressionLevel Optimal -Force
Pop-Location

# Rename to XPI
Move-Item 'temp-simple-bg.zip' $xpiName

# Clean up
Remove-Item $buildDir -Recurse -Force

Write-Host "Simple background XPI created: $xpiName"
