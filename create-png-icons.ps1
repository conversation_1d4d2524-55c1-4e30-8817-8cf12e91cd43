# PowerShell script to create PNG icons from SVG files
# Requires ImageMagick or similar tool to be installed

Write-Host "Creating PNG icons from SVG files..." -ForegroundColor Green

# Check if ImageMagick is available
$magickPath = Get-Command "magick" -ErrorAction SilentlyContinue
if (-not $magickPath) {
    Write-Host "ImageMagick not found. Please install ImageMagick to convert SVG to PNG." -ForegroundColor Red
    Write-Host "Download from: https://imagemagick.org/script/download.php#windows" -ForegroundColor Yellow
    Write-Host ""
    Write-Host "Alternative: Create PNG icons manually using any image editor:" -ForegroundColor Yellow
    Write-Host "- Create 16x16, 32x32, 48x48, 64x64, 128x128 pixel PNG files" -ForegroundColor Gray
    Write-Host "- Use a simple design with 'AI' text on blue background" -ForegroundColor Gray
    Write-Host "- Save as icon-16.png, icon-32.png, etc. in the icons/ folder" -ForegroundColor Gray
    exit 1
}

# Icon sizes to create
$sizes = @(16, 32, 48, 64, 128)
$iconsDir = "icons"

# Ensure icons directory exists
if (-not (Test-Path $iconsDir)) {
    New-Item -ItemType Directory -Path $iconsDir | Out-Null
}

# Convert each SVG to PNG
foreach ($size in $sizes) {
    $svgFile = Join-Path $iconsDir "icon-$size.svg"
    $pngFile = Join-Path $iconsDir "icon-$size.png"
    
    if (Test-Path $svgFile) {
        Write-Host "Converting icon-$size.svg to PNG..." -ForegroundColor Yellow
        
        try {
            # Use ImageMagick to convert SVG to PNG
            & magick $svgFile -resize "${size}x${size}" $pngFile
            
            if (Test-Path $pngFile) {
                Write-Host "  Created: $pngFile" -ForegroundColor Green
            } else {
                Write-Host "  Failed to create: $pngFile" -ForegroundColor Red
            }
        } catch {
            Write-Host "  Error converting $svgFile : $($_.Exception.Message)" -ForegroundColor Red
        }
    } else {
        Write-Host "  SVG file not found: $svgFile" -ForegroundColor Red
    }
}

# Check if all PNG files were created
$allCreated = $true
foreach ($size in $sizes) {
    $pngFile = Join-Path $iconsDir "icon-$size.png"
    if (-not (Test-Path $pngFile)) {
        $allCreated = $false
        break
    }
}

if ($allCreated) {
    Write-Host ""
    Write-Host "All PNG icons created successfully!" -ForegroundColor Green
    Write-Host "Now updating manifest.json to use PNG icons..." -ForegroundColor Yellow
    
    # Update manifest.json to use PNG icons
    $manifestPath = "manifest.json"
    if (Test-Path $manifestPath) {
        $manifest = Get-Content $manifestPath -Raw | ConvertFrom-Json
        
        # Add browser_action icons
        if (-not $manifest.browser_action) {
            $manifest.browser_action = @{}
        }
        $manifest.browser_action.default_icon = @{
            "16" = "icons/icon-16.png"
            "32" = "icons/icon-32.png"
            "48" = "icons/icon-48.png"
            "128" = "icons/icon-128.png"
        }
        
        # Add main icons
        $manifest.icons = @{
            "16" = "icons/icon-16.png"
            "32" = "icons/icon-32.png"
            "48" = "icons/icon-48.png"
            "128" = "icons/icon-128.png"
        }
        
        # Save updated manifest
        $manifest | ConvertTo-Json -Depth 10 | Set-Content $manifestPath
        Write-Host "  Updated manifest.json with PNG icon references" -ForegroundColor Green
        
        Write-Host ""
        Write-Host "Ready to rebuild XPI with proper PNG icons!" -ForegroundColor Green
        Write-Host "Run: .\build.ps1" -ForegroundColor Cyan
    }
} else {
    Write-Host ""
    Write-Host "Some PNG icons were not created. Please check the errors above." -ForegroundColor Red
    Write-Host "You can create PNG icons manually and place them in the icons/ folder." -ForegroundColor Yellow
}

Write-Host ""
Write-Host "Icon creation process completed." -ForegroundColor Green
