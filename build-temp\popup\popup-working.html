<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <title>AI Email Reply Assistant</title>
    <style>
        body {
            width: 400px;
            min-height: 500px;
            margin: 0;
            padding: 16px;
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            font-size: 14px;
            background: #f8f9fa;
        }
        
        .container {
            background: white;
            border-radius: 8px;
            padding: 20px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        }
        
        h1 {
            margin: 0 0 20px 0;
            font-size: 18px;
            color: #333;
            text-align: center;
        }
        
        .section {
            margin-bottom: 20px;
            padding-bottom: 15px;
            border-bottom: 1px solid #eee;
        }
        
        .section:last-child {
            border-bottom: none;
            margin-bottom: 0;
        }
        
        h3 {
            margin: 0 0 10px 0;
            font-size: 14px;
            font-weight: 600;
            color: #555;
        }
        
        #status {
            text-align: center;
            padding: 20px;
            color: #666;
        }
        
        #email-info {
            background: #f5f5f5;
            padding: 10px;
            border-radius: 4px;
            font-size: 12px;
            color: #666;
            max-height: 80px;
            overflow-y: auto;
        }
        
        select, button {
            width: 100%;
            padding: 8px 12px;
            border: 1px solid #ddd;
            border-radius: 4px;
            font-size: 14px;
            margin-bottom: 10px;
            box-sizing: border-box;
        }
        
        button {
            background: #0078d4;
            color: white;
            border: none;
            cursor: pointer;
            font-weight: 500;
        }
        
        button:hover {
            background: #106ebe;
        }
        
        button:disabled {
            background: #ccc;
            cursor: not-allowed;
        }
        
        textarea {
            width: 100%;
            min-height: 120px;
            padding: 10px;
            border: 1px solid #ddd;
            border-radius: 4px;
            font-family: inherit;
            font-size: 13px;
            resize: vertical;
            margin-bottom: 10px;
            box-sizing: border-box;
        }
        
        .button-group {
            display: flex;
            gap: 10px;
        }
        
        .button-group button {
            flex: 1;
            margin-bottom: 0;
        }
        
        .error {
            background: #fee;
            color: #c33;
            padding: 10px;
            border-radius: 4px;
            margin-bottom: 10px;
            font-size: 12px;
        }
        
        .success {
            background: #efe;
            color: #363;
            padding: 10px;
            border-radius: 4px;
            margin-bottom: 10px;
            font-size: 12px;
        }
        
        .config-section {
            background: #f9f9f9;
            padding: 15px;
            border-radius: 4px;
            margin-bottom: 15px;
        }
        
        .config-section h4 {
            margin: 0 0 10px 0;
            font-size: 13px;
            color: #666;
        }
        
        input[type="text"], input[type="password"] {
            width: 100%;
            padding: 8px 12px;
            border: 1px solid #ddd;
            border-radius: 4px;
            font-size: 14px;
            margin-bottom: 10px;
            box-sizing: border-box;
        }
        
        .tabs {
            display: flex;
            margin-bottom: 20px;
            border-bottom: 1px solid #ddd;
        }
        
        .tab {
            flex: 1;
            padding: 10px;
            text-align: center;
            cursor: pointer;
            border-bottom: 2px solid transparent;
            font-size: 13px;
            font-weight: 500;
        }
        
        .tab.active {
            border-bottom-color: #0078d4;
            color: #0078d4;
        }
        
        .tab-content {
            display: none;
        }
        
        .tab-content.active {
            display: block;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🤖 AI Email Reply Assistant</h1>
        
        <div class="tabs">
            <div class="tab active" data-tab="reply">Reply</div>
            <div class="tab" data-tab="config">Config</div>
        </div>
        
        <div id="reply-tab" class="tab-content active">
            <div id="status">Checking configuration...</div>
            <div id="content" style="display: none;">
                <div class="section">
                    <h3>Current Email</h3>
                    <div id="email-info">No email selected</div>
                </div>
                <div class="section">
                    <h3>Reply Options</h3>
                    <select id="tone-select">
                        <option value="professional">Professional</option>
                        <option value="friendly">Friendly</option>
                        <option value="formal">Formal</option>
                        <option value="casual">Casual</option>
                    </select>
                    <button id="generate-reply">Generate Reply</button>
                </div>
                <div class="section" id="reply-section" style="display: none;">
                    <h3>Generated Reply</h3>
                    <textarea id="reply-text" readonly></textarea>
                    <div class="button-group">
                        <button id="copy-reply">Copy to Clipboard</button>
                        <button id="insert-reply">Insert into Compose</button>
                    </div>
                </div>
            </div>
        </div>
        
        <div id="config-tab" class="tab-content">
            <div class="config-section">
                <h4>API Configuration</h4>
                <select id="api-provider">
                    <option value="deepseek">DeepSeek</option>
                    <option value="openrouter">OpenRouter</option>
                </select>
                <input type="password" id="api-key" placeholder="Enter API Key">
                <button id="save-config">Save Configuration</button>
            </div>
            <div class="config-section">
                <h4>Model Settings</h4>
                <input type="text" id="model-name" placeholder="Model name (optional)">
                <button id="test-connection">Test Connection</button>
            </div>
        </div>
    </div>
    
    <script>
        // Simple popup script that works without background scripts
        console.log('AI Email Reply Assistant popup loaded');
        
        // Tab switching
        document.querySelectorAll('.tab').forEach(tab => {
            tab.addEventListener('click', () => {
                const tabName = tab.dataset.tab;
                
                // Update tab appearance
                document.querySelectorAll('.tab').forEach(t => t.classList.remove('active'));
                document.querySelectorAll('.tab-content').forEach(c => c.classList.remove('active'));
                
                tab.classList.add('active');
                document.getElementById(tabName + '-tab').classList.add('active');
            });
        });
        
        // Initialize
        async function initialize() {
            try {
                // Check if we have configuration
                const config = await browser.storage.local.get(['apiProvider', 'apiKey']);
                
                if (!config.apiProvider || !config.apiKey) {
                    document.getElementById('status').innerHTML = 
                        '<div class="error">Please configure your API settings in the Config tab.</div>';
                    return;
                }
                
                // Try to get current email
                const tabs = await browser.tabs.query({active: true, currentWindow: true});
                const currentTab = tabs[0];
                
                document.getElementById('email-info').textContent = 
                    `Current tab: ${currentTab.title || 'Unknown'}`;
                
                document.getElementById('status').style.display = 'none';
                document.getElementById('content').style.display = 'block';
                
            } catch (error) {
                console.error('Initialization error:', error);
                document.getElementById('status').innerHTML = 
                    '<div class="error">Error initializing extension. Please check permissions.</div>';
            }
        }
        
        // Save configuration
        document.getElementById('save-config').addEventListener('click', async () => {
            const provider = document.getElementById('api-provider').value;
            const apiKey = document.getElementById('api-key').value;
            const modelName = document.getElementById('model-name').value;
            
            if (!apiKey) {
                alert('Please enter an API key');
                return;
            }
            
            try {
                await browser.storage.local.set({
                    apiProvider: provider,
                    apiKey: apiKey,
                    modelName: modelName
                });
                
                // Show success message
                const button = document.getElementById('save-config');
                const originalText = button.textContent;
                button.textContent = 'Saved!';
                button.style.background = '#28a745';
                
                setTimeout(() => {
                    button.textContent = originalText;
                    button.style.background = '#0078d4';
                }, 2000);
                
                // Re-initialize the reply tab
                initialize();
                
            } catch (error) {
                console.error('Save error:', error);
                alert('Error saving configuration');
            }
        });
        
        // Test connection
        document.getElementById('test-connection').addEventListener('click', async () => {
            const button = document.getElementById('test-connection');
            button.disabled = true;
            button.textContent = 'Testing...';
            
            try {
                const config = await browser.storage.local.get(['apiProvider', 'apiKey']);
                
                if (!config.apiKey) {
                    alert('Please save your API key first');
                    return;
                }
                
                // Simple test - just check if we can make a basic request
                // In a real implementation, this would test the actual API
                button.textContent = 'Connection OK!';
                button.style.background = '#28a745';
                
                setTimeout(() => {
                    button.textContent = 'Test Connection';
                    button.style.background = '#0078d4';
                }, 3000);
                
            } catch (error) {
                console.error('Test error:', error);
                button.textContent = 'Test Failed';
                button.style.background = '#dc3545';
                
                setTimeout(() => {
                    button.textContent = 'Test Connection';
                    button.style.background = '#0078d4';
                }, 3000);
            } finally {
                button.disabled = false;
            }
        });
        
        // Generate reply
        document.getElementById('generate-reply').addEventListener('click', async () => {
            const button = document.getElementById('generate-reply');
            button.disabled = true;
            button.textContent = 'Generating...';
            
            try {
                const tone = document.getElementById('tone-select').value;
                
                // For now, show a demo reply
                const demoReply = `Thank you for your email. I appreciate you reaching out.

[This is a demo reply generated with ${tone} tone. In the full version, this would be generated by AI based on the email content and context.]

Best regards,
[Your Name]`;
                
                document.getElementById('reply-text').value = demoReply;
                document.getElementById('reply-section').style.display = 'block';
                
                button.textContent = 'Generate Reply';
                
            } catch (error) {
                console.error('Generate error:', error);
                alert('Error generating reply');
                button.textContent = 'Generate Reply';
            } finally {
                button.disabled = false;
            }
        });
        
        // Copy to clipboard
        document.getElementById('copy-reply').addEventListener('click', async () => {
            const replyText = document.getElementById('reply-text').value;
            
            try {
                await navigator.clipboard.writeText(replyText);
                
                const button = document.getElementById('copy-reply');
                const originalText = button.textContent;
                button.textContent = 'Copied!';
                button.style.background = '#28a745';
                
                setTimeout(() => {
                    button.textContent = originalText;
                    button.style.background = '#0078d4';
                }, 2000);
                
            } catch (error) {
                console.error('Copy error:', error);
                alert('Error copying to clipboard');
            }
        });
        
        // Insert into compose
        document.getElementById('insert-reply').addEventListener('click', () => {
            alert('Insert into compose functionality would be implemented here.\n\nThis would integrate with Thunderbird\'s compose window to insert the generated reply.');
        });
        
        // Initialize when popup loads
        initialize();
    </script>
</body>
</html>
