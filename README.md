# AI Email Reply Assistant for Thunderbird

A production-ready Thunderbird MailExtension that generates AI-powered email replies using DeepSeek or OpenRouter APIs with advanced tone matching and context awareness.

## 🚀 Features

- 📧 **Smart Email Fetching**: Automatically fetches the 20 most recent emails from Inbox and Sent folders across all configured accounts
- 🤖 **AI-Powered Replies**: Generate contextual replies using DeepSeek or OpenRouter APIs
- 🎯 **Advanced Tone Matching**: Analyzes and matches the tone of original emails (formal, casual, urgent, technical, etc.)
- 📝 **Live Preview**: Preview and edit generated replies before sending
- ⚙️ **Easy Configuration**: Secure API key management through built-in settings
- 🔄 **Thunderbird Integration**: Seamlessly opens replies in Thunderbird's native compose window
- 🛡️ **Robust Error Handling**: Comprehensive error handling with user-friendly feedback
- 📊 **Context Analysis**: Understands email context (business inquiry, project discussion, etc.)

## 📋 Requirements

- **Thunderbird**: Version 78.0 or later
- **API Keys**: Valid API keys for DeepSeek and/or OpenRouter
- **Internet Connection**: Required for AI API calls

## 🔧 Installation

### Method 1: XPI Installation (Recommended)

1. **Download the XPI file**:
   - Download `ai-email-reply-assistant-1.0.0.xpi` from the releases
   - Or build it yourself using the build scripts

2. **Install in Thunderbird**:
   - Open Thunderbird
   - Go to `Tools` > `Add-ons and Themes`
   - Click the gear icon (⚙️) and select `Install Add-on From File`
   - Select the `ai-email-reply-assistant-1.0.0.xpi` file
   - Click `Open` to install

3. **Configure API Keys**:
   - Click the AI Email Reply Assistant button in the toolbar
   - Click the `⚙️ Settings` button
   - Enter your API keys and save

### Method 2: Build from Source

1. **Clone and build**:
   ```bash
   git clone https://github.com/your-repo/ai-email-reply-assistant.git
   cd ai-email-reply-assistant

   # Windows:
   .\build.ps1

   # Linux/macOS:
   ./build.sh
   ```

2. **Install the generated XPI**:
   - Follow Method 1 steps 2-3 above

### Method 2: Development Installation

1. **Enable debugging**:
   - In Thunderbird, go to `Tools` > `Developer Tools` > `Debug Add-ons`
   - Click `Load Temporary Add-on`
   - Select the `manifest.json` file

## 🔑 API Keys Setup

### DeepSeek API
1. Visit [DeepSeek Platform](https://platform.deepseek.com/)
2. Create an account and navigate to API Keys
3. Generate a new API key
4. Copy the key (starts with `sk-`)

### OpenRouter API
1. Visit [OpenRouter](https://openrouter.ai/)
2. Sign up and go to your dashboard
3. Generate an API key
4. Copy the key for configuration

## 📖 Usage Guide

### Basic Usage
1. **Launch the Assistant**: Click the AI Email Reply Assistant button in Thunderbird's toolbar
2. **Review Emails**: The addon automatically loads recent emails from all accounts
3. **Select Emails**: Check the emails you want to generate replies for
4. **Choose AI Model**: Select DeepSeek or OpenRouter from the dropdown
5. **Generate Reply**: Click "✨ Generate Replies"
6. **Preview & Edit**: Review the generated reply in the preview window
7. **Send**: Either copy to clipboard or open directly in Thunderbird's compose window

### Advanced Features

#### Tone Matching
The AI automatically analyzes and matches:
- **Formal tone**: Professional language, proper structure
- **Casual tone**: Conversational style, friendly approach
- **Urgent tone**: Direct, time-sensitive responses
- **Technical tone**: Precise, technical vocabulary
- **Business tone**: Professional, goal-oriented communication

#### Context Awareness
The system recognizes different email contexts:
- Business inquiries
- Project discussions
- Meeting coordination
- Technical support
- Follow-up communications
- Introductions
- Collaborations

## 🏗️ Architecture

### Core Components

```
ai-email-reply-assistant/
├── manifest.json              # Extension configuration
├── background.js              # Main background script
├── popup/
│   ├── popup.html            # Main UI interface
│   └── popup.js              # UI logic and interactions
├── scripts/
│   ├── ai-integration.js     # AI API integration
│   ├── config-manager.js     # Configuration management
│   └── error-handler.js      # Error handling and validation
├── styles/
│   └── popup.css             # UI styling
└── icons/                    # Extension icons
```

### Key Modules

1. **AI Integration** (`scripts/ai-integration.js`)
   - Handles API calls to DeepSeek and OpenRouter
   - Advanced prompt engineering
   - Tone and context analysis

2. **Configuration Manager** (`scripts/config-manager.js`)
   - Secure API key storage
   - Configuration validation
   - Settings management

3. **Error Handler** (`scripts/error-handler.js`)
   - Comprehensive error categorization
   - User-friendly error messages
   - Debugging and logging

## 🧪 Testing

### Manual Testing Checklist

- [ ] **Installation**: Addon installs without errors
- [ ] **Configuration**: API keys can be set and saved
- [ ] **Email Fetching**: Recent emails load correctly
- [ ] **Email Selection**: Multiple emails can be selected
- [ ] **Reply Generation**: AI generates appropriate replies
- [ ] **Tone Matching**: Replies match original email tone
- [ ] **Preview**: Generated replies display correctly
- [ ] **Compose Integration**: Opens in Thunderbird compose window
- [ ] **Error Handling**: Graceful error handling for invalid API keys
- [ ] **Multiple Accounts**: Works with multiple email accounts

### Test Scenarios

1. **Formal Business Email**:
   - Original: Professional inquiry about services
   - Expected: Formal, structured response

2. **Casual Personal Email**:
   - Original: Friendly, informal message
   - Expected: Casual, warm response

3. **Technical Support Email**:
   - Original: Technical problem description
   - Expected: Technical, solution-oriented response

4. **Urgent Request**:
   - Original: Time-sensitive request
   - Expected: Direct, immediate response

## 🔍 Troubleshooting

### Common Issues

#### "No emails found"
- **Cause**: No recent emails in Inbox/Sent folders
- **Solution**: Send/receive some emails first

#### "API key not configured"
- **Cause**: Missing or invalid API key
- **Solution**: Check API key in settings, ensure it's valid

#### "Network error"
- **Cause**: Internet connection or firewall issues
- **Solution**: Check internet connection and firewall settings

#### "Permission error"
- **Cause**: Insufficient addon permissions
- **Solution**: Reinstall the addon

### Debug Mode

Enable debug logging:
1. Open Thunderbird's Error Console (`Tools` > `Developer Tools` > `Error Console`)
2. Look for messages starting with "AI Email Reply Assistant"
3. Check for detailed error information

## 🔒 Security & Privacy

- **API Keys**: Stored locally in Thunderbird's secure storage
- **Email Content**: Only sent to selected AI provider
- **No Data Collection**: No user data is collected or stored by the addon
- **Secure Communication**: All API calls use HTTPS encryption

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Test thoroughly
5. Submit a pull request

## 📄 License

MIT License - see [LICENSE](LICENSE) file for details.

## 🆘 Support

- **Issues**: Report bugs on GitHub Issues
- **Documentation**: Check this README and inline code comments
- **Community**: Join discussions in GitHub Discussions

## 🔄 Changelog

### Version 1.0.0
- Initial release
- DeepSeek and OpenRouter integration
- Advanced tone matching
- Context-aware reply generation
- Thunderbird compose integration
- Comprehensive error handling
