// Background script for AI Email Reply Assistant
console.log("AI Email Reply Assistant background script loaded");

// Import modules
importScripts('scripts/ai-integration.js');
importScripts('scripts/config-manager.js');
importScripts('scripts/error-handler.js');

const aiIntegration = new AIIntegration();
const configManager = new ConfigManager();
const errorHandler = new ErrorHandler();

// Initialize the addon
browser.runtime.onInstalled.addListener(async (details) => {
  if (details.reason === "install") {
    console.log("AI Email Reply Assistant installed");

    // Initialize configuration using config manager
    try {
      const configResult = await configManager.loadConfig();
      if (configResult.success) {
        console.log("Configuration initialized successfully");
      } else {
        console.warn("Configuration initialization had issues:", configResult.error);
      }
    } catch (error) {
      console.error("Error initializing configuration:", error);
    }
  }
});

// Handle messages from popup
browser.runtime.onMessage.addListener(async (message, sender, sendResponse) => {
  console.log("Background received message:", message);
  
  switch (message.action) {
    case "getRecentEmails":
      return handleGetRecentEmails();
    case "generateReply":
      return handleGenerateReply(message.data);
    case "getConfig":
      return handleGetConfig();
    case "saveConfig":
      return handleSaveConfig(message.data);
    case "openCompose":
      return handleOpenCompose(message.data);
    default:
      console.warn("Unknown action:", message.action);
      return { success: false, error: "Unknown action" };
  }
});

async function handleGetRecentEmails() {
  try {
    const accounts = await browser.accounts.list();
    const allEmails = [];

    for (const account of accounts) {
      console.log(`Processing account: ${account.name}`);

      // Get all folders for this account (including nested ones)
      const allFolders = await getAllFoldersRecursive(account);

      // Filter for inbox and sent folders
      const targetFolders = allFolders.filter(folder =>
        folder.type === "inbox" ||
        folder.type === "sent" ||
        folder.name.toLowerCase().includes("inbox") ||
        folder.name.toLowerCase().includes("sent")
      );

      for (const folder of targetFolders) {
        try {
          console.log(`Processing folder: ${folder.name} (${folder.type})`);
          const messages = await browser.messages.list(folder);

          if (!messages.messages || messages.messages.length === 0) {
            console.log(`No messages found in folder: ${folder.name}`);
            continue;
          }

          // Get the 10 most recent messages from this folder
          const recentMessages = messages.messages
            .sort((a, b) => new Date(b.date) - new Date(a.date))
            .slice(0, 10);

          for (const message of recentMessages) {
            try {
              const fullMessage = await browser.messages.getFull(message.id);
              const preview = await getMessagePreview(fullMessage);

              allEmails.push({
                id: message.id,
                subject: message.subject || 'No Subject',
                author: message.author || 'Unknown Sender',
                date: message.date,
                folder: folder.name,
                folderType: folder.type,
                accountName: account.name,
                preview: preview,
                fullMessage: fullMessage
              });
            } catch (msgError) {
              console.warn(`Error processing message ${message.id}:`, msgError);
            }
          }
        } catch (folderError) {
          console.warn(`Error processing folder ${folder.name}:`, folderError);
        }
      }
    }

    // Sort all emails by date and take top 20
    const sortedEmails = allEmails
      .sort((a, b) => new Date(b.date) - new Date(a.date))
      .slice(0, 20);

    console.log(`Found ${sortedEmails.length} total emails`);
    return { success: true, emails: sortedEmails };
  } catch (error) {
    console.error("Error fetching emails:", error);
    return { success: false, error: error.message };
  }
}

async function getAllFoldersRecursive(account) {
  const allFolders = [];

  async function processFolders(folders) {
    for (const folder of folders) {
      allFolders.push(folder);

      // Get subfolders recursively
      try {
        const subFolders = await browser.folders.getSubFolders(folder);
        if (subFolders && subFolders.length > 0) {
          await processFolders(subFolders);
        }
      } catch (error) {
        console.warn(`Error getting subfolders for ${folder.name}:`, error);
      }
    }
  }

  try {
    const rootFolders = await browser.folders.getSubFolders(account);
    await processFolders(rootFolders);
  } catch (error) {
    console.error(`Error getting folders for account ${account.name}:`, error);
  }

  return allFolders;
}

async function getMessagePreview(fullMessage) {
  try {
    let textContent = "";

    // Extract text content from the message
    if (fullMessage.parts && fullMessage.parts.length > 0) {
      // Look for text/plain first
      for (const part of fullMessage.parts) {
        if (part.contentType === "text/plain" && part.body) {
          textContent = part.body;
          break;
        }
      }

      // If no plain text found, look for text/html and strip tags
      if (!textContent) {
        for (const part of fullMessage.parts) {
          if (part.contentType === "text/html" && part.body) {
            textContent = stripHtmlTags(part.body);
            break;
          }
        }
      }
    }

    // If still no content, try to get from the message body directly
    if (!textContent && fullMessage.body) {
      textContent = typeof fullMessage.body === 'string' ? fullMessage.body : '';
    }

    if (textContent) {
      // Clean up the text and limit length
      textContent = textContent
        .replace(/\s+/g, ' ')  // Replace multiple whitespace with single space
        .trim();

      return textContent.length > 200
        ? textContent.substring(0, 200) + "..."
        : textContent;
    }

    return "No preview available";
  } catch (error) {
    console.error("Error getting message preview:", error);
    return "Preview unavailable";
  }
}

function stripHtmlTags(html) {
  try {
    // Simple HTML tag removal - in a real extension you might want to use a proper HTML parser
    return html
      .replace(/<[^>]*>/g, '')  // Remove HTML tags
      .replace(/&nbsp;/g, ' ')  // Replace &nbsp; with space
      .replace(/&amp;/g, '&')   // Replace &amp; with &
      .replace(/&lt;/g, '<')    // Replace &lt; with <
      .replace(/&gt;/g, '>')    // Replace &gt; with >
      .replace(/&quot;/g, '"')  // Replace &quot; with "
      .trim();
  } catch (error) {
    console.error("Error stripping HTML tags:", error);
    return html;
  }
}

async function handleGenerateReply(data) {
  try {
    // Validate input data
    if (!data || !data.emailId || !data.model) {
      const error = new Error('Missing required parameters: emailId and model are required');
      const errorInfo = errorHandler.handleError(error, 'handleGenerateReply');
      return { success: false, error: errorInfo.message, details: errorInfo };
    }

    const { emailId, model, customPrompt } = data;

    // Get configuration
    const config = await handleGetConfig();
    if (!config.success) {
      const error = new Error('Configuration not available');
      const errorInfo = errorHandler.handleError(error, 'handleGenerateReply');
      return { success: false, error: errorInfo.message, details: errorInfo };
    }

    // Validate configuration
    const configValidation = errorHandler.validateConfiguration(config.config);
    if (!configValidation.valid) {
      const error = new Error(`Configuration validation failed: ${configValidation.errors.join(', ')}`);
      const errorInfo = errorHandler.handleError(error, 'handleGenerateReply');
      return { success: false, error: errorInfo.message, details: errorInfo };
    }

    // Get the full message details
    const message = await browser.messages.getFull(emailId);
    if (!message) {
      const error = new Error(`Email with ID ${emailId} not found`);
      const errorInfo = errorHandler.handleError(error, 'handleGenerateReply');
      return { success: false, error: errorInfo.message, details: errorInfo };
    }

    // Generate reply using selected AI model
    const reply = await generateAIReply(message, model, config.config, customPrompt);

    return { success: true, reply: reply };
  } catch (error) {
    const errorInfo = errorHandler.handleError(error, 'handleGenerateReply');
    return { success: false, error: errorInfo.message, details: errorInfo };
  }
}

async function generateAIReply(message, model, config, customPrompt) {
  try {
    // Get the appropriate API key based on the selected model
    let apiKey;
    if (model === 'deepseek') {
      apiKey = config.deepseek_api_key;
    } else if (model === 'openrouter') {
      apiKey = config.openrouter_api_key;
    } else {
      throw new Error(`Unsupported model: ${model}`);
    }

    if (!apiKey) {
      throw new Error(`API key not configured for ${model}`);
    }

    // Prepare email data for AI processing
    const emailData = {
      id: message.id,
      subject: message.subject || 'No Subject',
      author: message.author || 'Unknown Sender',
      date: message.date,
      preview: await getMessagePreview(message),
      fullMessage: message
    };

    // Generate reply using AI integration
    const result = await aiIntegration.generateReply(emailData, model, apiKey, customPrompt);

    if (result.success) {
      return result.reply;
    } else {
      throw new Error(result.error);
    }
  } catch (error) {
    console.error("Error in generateAIReply:", error);
    throw error;
  }
}

async function handleGetConfig() {
  return await configManager.loadConfig();
}

async function handleSaveConfig(config) {
  return await configManager.saveConfig(config);
}

async function handleOpenCompose(data) {
  try {
    const { originalMessageId, composeDetails } = data;

    // Get the original message for reply context
    const originalMessage = await browser.messages.getFull(originalMessageId);

    // Create compose window with reply context
    const composeTab = await browser.compose.beginReply(originalMessageId, "replyToSender", {
      body: composeDetails.body,
      isReply: true
    });

    if (composeTab) {
      console.log("Compose window opened successfully");
      return { success: true, composeTabId: composeTab.id };
    } else {
      throw new Error("Failed to create compose window");
    }
  } catch (error) {
    console.error("Error opening compose window:", error);

    // Fallback: try to open a new compose window
    try {
      const composeTab = await browser.compose.beginNew({
        to: data.composeDetails.to,
        subject: data.composeDetails.subject,
        body: data.composeDetails.body
      });

      if (composeTab) {
        console.log("Fallback compose window opened");
        return { success: true, composeTabId: composeTab.id };
      }
    } catch (fallbackError) {
      console.error("Fallback compose also failed:", fallbackError);
    }

    return { success: false, error: error.message };
  }
}
