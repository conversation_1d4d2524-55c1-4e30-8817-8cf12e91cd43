{"test_configuration": {"description": "Test configuration for AI Email Reply Assistant", "version": "1.0.0", "test_api_keys": {"deepseek_api_key": "sk-test-deepseek-key-for-testing-only", "openrouter_api_key": "sk-test-openrouter-key-for-testing-only", "default_model": "deepseek"}, "test_scenarios": [{"name": "Formal Business Email", "description": "Test tone matching for formal business communication", "sample_email": {"subject": "Proposal for Software Development Services", "author": "<EMAIL>", "content": "Dear Sir/<PERSON><PERSON>,\n\nI am writing to inquire about your software development services. We are looking for a reliable partner to help us develop a new customer management system.\n\nCould you please provide us with information about your capabilities and pricing?\n\nThank you for your time and consideration.\n\n<PERSON><PERSON><PERSON>,\n<PERSON>\nProject Manager"}, "expected_tone": "formal", "expected_context": "business inquiry"}, {"name": "Casual Personal Email", "description": "Test tone matching for casual personal communication", "sample_email": {"subject": "Weekend Plans", "author": "<EMAIL>", "content": "Hey there!\n\nHope you're doing well! I was wondering if you'd like to grab coffee this weekend? There's this new place downtown that I've been wanting to try.\n\nLet me know what you think!\n\nCheers,\n<PERSON>"}, "expected_tone": "casual", "expected_context": "general correspondence"}, {"name": "Technical Support Email", "description": "Test tone matching for technical support communication", "sample_email": {"subject": "API Integration Issue - Error 500", "author": "<EMAIL>", "content": "Hi Support Team,\n\nI'm experiencing a 500 Internal Server Error when trying to authenticate with your API. The error occurs consistently when making POST requests to /api/v1/auth/login.\n\nRequest details:\n- Endpoint: https://api.yourservice.com/v1/auth/login\n- Method: POST\n- Headers: Content-Type: application/json\n- Body: {\"username\": \"test\", \"password\": \"test123\"}\n\nCould you please help me resolve this issue?\n\nBest regards,\nAlex Chen\nSoftware Developer"}, "expected_tone": "technical", "expected_context": "technical support"}, {"name": "Urgent Request Email", "description": "Test tone matching for urgent communication", "sample_email": {"subject": "URGENT: Server Down - Need Immediate Assistance", "author": "<EMAIL>", "content": "URGENT ISSUE\n\nOur production server is down and we need immediate assistance. This is affecting all our customers and we're losing revenue every minute.\n\nPlease call me ASAP at +1-555-0123.\n\nTime is critical!\n\n<PERSON>\nIT Administrator"}, "expected_tone": "urgent", "expected_context": "technical support"}, {"name": "Meeting Coordination Email", "description": "Test tone matching for meeting coordination", "sample_email": {"subject": "Project Kickoff Meeting - Schedule Confirmation", "author": "<EMAIL>", "content": "Hello Team,\n\nI hope this email finds you well. I'm writing to confirm the schedule for our project kickoff meeting.\n\nProposed Details:\n- Date: Next Tuesday, March 15th\n- Time: 2:00 PM - 3:30 PM EST\n- Location: Conference Room A / Zoom link will be provided\n\nPlease confirm your availability by Friday.\n\nAgenda items:\n1. Project overview\n2. Team introductions\n3. Timeline discussion\n4. Next steps\n\nLooking forward to working with everyone!\n\nBest regards,\n<PERSON>\nProject Manager"}, "expected_tone": "business", "expected_context": "meeting coordination"}], "test_validation_rules": {"api_key_format": {"deepseek": "^sk-[a-zA-Z0-9]{20,}$", "openrouter": "^sk-[a-zA-Z0-9]{20,}$"}, "required_fields": ["deepseek_api_key", "openrouter_api_key", "default_model"], "valid_models": ["deepseek", "openrouter"]}, "test_error_scenarios": [{"name": "Invalid API Key", "description": "Test error handling for invalid API keys", "test_data": {"api_key": "invalid-key", "expected_error": "API_ERROR"}}, {"name": "Network Timeout", "description": "Test error handling for network timeouts", "test_data": {"timeout": 1, "expected_error": "NETWORK_ERROR"}}, {"name": "Missing Configuration", "description": "Test error handling for missing configuration", "test_data": {"config": null, "expected_error": "CONFIG_ERROR"}}, {"name": "Invalid Email Data", "description": "Test error handling for invalid email data", "test_data": {"email": {"id": null, "subject": "", "author": ""}, "expected_error": "VALIDATION_ERROR"}}], "performance_benchmarks": {"email_fetch_time": {"target": "< 2 seconds", "description": "Time to fetch 20 recent emails"}, "reply_generation_time": {"target": "< 10 seconds", "description": "Time to generate AI reply"}, "ui_response_time": {"target": "< 500ms", "description": "UI interaction response time"}}, "compatibility_matrix": {"thunderbird_versions": ["78.0", "91.0", "102.0", "115.0"], "operating_systems": ["Windows 10", "Windows 11", "macOS 10.15+", "Ubuntu 20.04+", "Fedora 35+"]}}, "manual_test_checklist": [{"category": "Installation", "tests": ["Addon installs without errors", "Toolbar button appears", "Popup opens correctly", "Settings modal functions"]}, {"category": "Configuration", "tests": ["API keys can be entered and saved", "Configuration validation works", "Invalid keys show appropriate errors", "Default model selection works"]}, {"category": "Em<PERSON>tching", "tests": ["Recent emails load from all accounts", "Email list displays correctly", "Email selection works", "Multiple account support"]}, {"category": "Reply Generation", "tests": ["AI generates appropriate replies", "Tone matching works correctly", "Context analysis is accurate", "Custom prompts are respected"]}, {"category": "User Interface", "tests": ["All buttons function correctly", "Modals open and close properly", "Status messages display", "Loading indicators work"]}, {"category": "Integration", "tests": ["Compose window opens correctly", "Reply content transfers properly", "Copy to clipboard works", "Email threading is maintained"]}, {"category": "Erro<PERSON>", "tests": ["Network errors handled gracefully", "API errors show user-friendly messages", "Configuration errors are clear", "Validation errors are helpful"]}], "automated_test_commands": {"note": "These would be implemented in a testing framework", "unit_tests": "npm test", "integration_tests": "npm run test:integration", "e2e_tests": "npm run test:e2e", "lint": "npm run lint", "coverage": "npm run test:coverage"}}