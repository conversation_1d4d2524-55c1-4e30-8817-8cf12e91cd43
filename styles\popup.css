/* AI Email Reply Assistant <PERSON><PERSON> Styles */

* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    font-size: 14px;
    line-height: 1.4;
    color: #333;
    background: #f5f5f5;
    width: 450px;
    max-height: 600px;
    overflow-y: auto;
}

.container {
    background: white;
    border-radius: 8px;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
    overflow: hidden;
}

/* Header */
.header {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    padding: 15px 20px;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.header h1 {
    font-size: 16px;
    font-weight: 600;
}

.refresh-btn {
    background: rgba(255, 255, 255, 0.2);
    border: none;
    color: white;
    padding: 8px;
    border-radius: 4px;
    cursor: pointer;
    font-size: 14px;
    transition: background 0.2s;
}

.refresh-btn:hover {
    background: rgba(255, 255, 255, 0.3);
}

/* Configuration Section */
.config-section {
    padding: 15px 20px;
    background: #f8f9fa;
    border-bottom: 1px solid #e9ecef;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.model-selector label {
    margin-right: 10px;
    font-weight: 500;
}

.model-selector select {
    padding: 6px 10px;
    border: 1px solid #ddd;
    border-radius: 4px;
    background: white;
}

.config-btn {
    background: #6c757d;
    color: white;
    border: none;
    padding: 8px 12px;
    border-radius: 4px;
    cursor: pointer;
    font-size: 12px;
    transition: background 0.2s;
}

.config-btn:hover {
    background: #5a6268;
}

/* Loading */
.loading {
    padding: 20px;
    text-align: center;
    color: #666;
}

.spinner {
    width: 20px;
    height: 20px;
    border: 2px solid #f3f3f3;
    border-top: 2px solid #667eea;
    border-radius: 50%;
    animation: spin 1s linear infinite;
    display: inline-block;
    margin-right: 10px;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* Email Section */
.email-section {
    padding: 0 20px;
}

.section-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 15px 0 10px;
    border-bottom: 1px solid #e9ecef;
}

.section-header h2 {
    font-size: 14px;
    font-weight: 600;
    color: #495057;
}

.email-count {
    font-size: 12px;
    color: #6c757d;
}

.email-list {
    max-height: 300px;
    overflow-y: auto;
}

.email-item {
    padding: 12px 0;
    border-bottom: 1px solid #f1f3f4;
    cursor: pointer;
    transition: background 0.2s;
}

.email-item:hover {
    background: #f8f9fa;
}

.email-item.selected {
    background: #e3f2fd;
    border-left: 3px solid #2196f3;
    padding-left: 9px;
}

.email-header {
    display: flex;
    align-items: center;
    margin-bottom: 5px;
}

.email-checkbox {
    margin-right: 10px;
}

.email-subject {
    font-weight: 500;
    color: #333;
    flex: 1;
    margin-right: 10px;
}

.email-folder {
    background: #e9ecef;
    color: #495057;
    padding: 2px 6px;
    border-radius: 3px;
    font-size: 10px;
    text-transform: uppercase;
}

.email-meta {
    font-size: 12px;
    color: #6c757d;
    margin-bottom: 5px;
}

.email-preview {
    font-size: 12px;
    color: #868e96;
    line-height: 1.3;
}

/* Action Section */
.action-section {
    padding: 15px 20px;
    background: #f8f9fa;
    border-top: 1px solid #e9ecef;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.selected-count {
    font-size: 12px;
    color: #6c757d;
}

.action-buttons {
    display: flex;
    gap: 10px;
}

.generate-btn {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    border: none;
    padding: 10px 16px;
    border-radius: 4px;
    cursor: pointer;
    font-size: 13px;
    font-weight: 500;
    transition: opacity 0.2s;
}

.generate-btn:disabled {
    opacity: 0.5;
    cursor: not-allowed;
}

.select-all-btn {
    background: #28a745;
    color: white;
    border: none;
    padding: 10px 16px;
    border-radius: 4px;
    cursor: pointer;
    font-size: 13px;
    transition: background 0.2s;
}

.select-all-btn:hover {
    background: #218838;
}

/* Status Bar */
.status-bar {
    padding: 8px 20px;
    background: #e9ecef;
    font-size: 12px;
    color: #6c757d;
    text-align: center;
}

/* Modal Styles */
.modal {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.5);
    display: flex;
    justify-content: center;
    align-items: center;
    z-index: 1000;
}

.modal-content {
    background: white;
    border-radius: 8px;
    width: 90%;
    max-width: 500px;
    max-height: 80%;
    overflow-y: auto;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.3);
}

.reply-modal {
    max-width: 600px;
}

.modal-header {
    padding: 20px;
    border-bottom: 1px solid #e9ecef;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.modal-header h2 {
    font-size: 16px;
    font-weight: 600;
}

.close-btn {
    background: none;
    border: none;
    font-size: 24px;
    cursor: pointer;
    color: #6c757d;
    padding: 0;
    width: 30px;
    height: 30px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.close-btn:hover {
    color: #333;
}

.modal-body {
    padding: 20px;
}

.config-group {
    margin-bottom: 15px;
}

.config-group label {
    display: block;
    margin-bottom: 5px;
    font-weight: 500;
}

.config-group input,
.config-group select {
    width: 100%;
    padding: 8px 12px;
    border: 1px solid #ddd;
    border-radius: 4px;
    font-size: 14px;
}

.original-email,
.generated-reply {
    margin-bottom: 20px;
}

.original-email h3,
.generated-reply h3 {
    font-size: 14px;
    margin-bottom: 10px;
    color: #495057;
}

#originalEmailContent {
    background: #f8f9fa;
    padding: 10px;
    border-radius: 4px;
    font-size: 12px;
    max-height: 100px;
    overflow-y: auto;
}

#replyContent {
    width: 100%;
    padding: 10px;
    border: 1px solid #ddd;
    border-radius: 4px;
    font-size: 14px;
    resize: vertical;
}

.modal-footer {
    padding: 20px;
    border-top: 1px solid #e9ecef;
    display: flex;
    justify-content: flex-end;
    gap: 10px;
}

.save-btn,
.copy-btn,
.compose-btn {
    background: #007bff;
    color: white;
    border: none;
    padding: 10px 16px;
    border-radius: 4px;
    cursor: pointer;
    font-size: 13px;
    transition: background 0.2s;
}

.save-btn:hover,
.copy-btn:hover,
.compose-btn:hover {
    background: #0056b3;
}

.cancel-btn {
    background: #6c757d;
    color: white;
    border: none;
    padding: 10px 16px;
    border-radius: 4px;
    cursor: pointer;
    font-size: 13px;
    transition: background 0.2s;
}

.cancel-btn:hover {
    background: #5a6268;
}
