// AI Integration Module for Email Reply Assistant

class AIIntegration {
  constructor() {
    this.models = {
      deepseek: {
        name: "DeepSeek Chat",
        endpoint: "https://api.deepseek.com/v1/chat/completions",
        model: "deepseek-chat",
        maxTokens: 2000
      },
      openrouter: {
        name: "OpenRouter GPT-4",
        endpoint: "https://openrouter.ai/api/v1/chat/completions", 
        model: "openai/gpt-4",
        maxTokens: 2000
      }
    };
  }

  async generateReply(emailData, modelType, apiKey, customPrompt = null) {
    try {
      const model = this.models[modelType];
      if (!model) {
        throw new Error(`Unsupported model type: ${modelType}`);
      }

      if (!apiKey) {
        throw new Error(`API key not provided for ${modelType}`);
      }

      const prompt = this.buildPrompt(emailData, customPrompt);
      const response = await this.callAIAPI(model, apiKey, prompt, modelType);
      
      return {
        success: true,
        reply: response,
        model: modelType,
        timestamp: new Date().toISOString()
      };
    } catch (error) {
      console.error(`Error generating reply with ${modelType}:`, error);
      return {
        success: false,
        error: error.message,
        model: modelType
      };
    }
  }

  buildPrompt(emailData, customPrompt) {
    const { subject, author, preview, fullMessage } = emailData;

    // Extract email content for context
    let emailContent = this.extractEmailContent(fullMessage);

    // Analyze tone and context
    const toneAnalysis = this.analyzeTone(emailContent, subject);
    const contextAnalysis = this.analyzeContext(emailContent, subject, author);

    // Build context-aware prompt with advanced tone matching
    let prompt = `You are an expert email communication assistant. Analyze the following email and generate a contextually appropriate reply that perfectly matches the sender's communication style and tone.

ORIGINAL EMAIL ANALYSIS:
Subject: ${subject}
From: ${author}
Content: ${emailContent}

TONE ANALYSIS: ${toneAnalysis}
CONTEXT ANALYSIS: ${contextAnalysis}

REPLY GENERATION INSTRUCTIONS:
1. TONE MATCHING: Mirror the exact tone of the original email (${toneAnalysis})
2. COMMUNICATION STYLE: Match the sender's level of formality, vocabulary, and sentence structure
3. CONTENT RELEVANCE: Address all key points and questions from the original email
4. RELATIONSHIP CONTEXT: Consider the professional/personal relationship indicated by the communication style
5. RESPONSE COMPLETENESS: Provide a complete response that moves the conversation forward appropriately
6. NATURAL FLOW: Ensure the reply feels like a natural continuation of the conversation

SPECIFIC TONE GUIDELINES:
- If formal: Use professional language, proper titles, structured paragraphs
- If casual: Use conversational language, contractions, friendly tone
- If urgent: Acknowledge urgency and provide timely, direct responses
- If technical: Match technical vocabulary and precision level
- If friendly: Maintain warmth while being professional

${customPrompt ? `ADDITIONAL CUSTOM INSTRUCTIONS: ${customPrompt}` : ''}

Generate ONLY the email reply content (no subject line, no explanations, no formatting markers). Start directly with the greeting and end with an appropriate closing:`;

    return prompt;
  }

  analyzeTone(content, subject) {
    const text = (content + ' ' + subject).toLowerCase();

    // Tone indicators
    const toneIndicators = {
      formal: ['dear', 'sincerely', 'regards', 'please find', 'i am writing to', 'thank you for your', 'i would like to'],
      casual: ['hi', 'hey', 'thanks', 'let me know', 'sounds good', 'no problem', 'talk soon'],
      urgent: ['urgent', 'asap', 'immediately', 'deadline', 'time-sensitive', 'rush', 'priority'],
      friendly: ['hope you', 'how are you', 'great to hear', 'looking forward', 'excited', 'wonderful'],
      technical: ['implementation', 'configuration', 'parameters', 'specifications', 'documentation', 'api', 'database'],
      business: ['meeting', 'proposal', 'contract', 'budget', 'timeline', 'deliverables', 'stakeholders']
    };

    let toneScores = {};

    for (const [tone, indicators] of Object.entries(toneIndicators)) {
      toneScores[tone] = indicators.filter(indicator => text.includes(indicator)).length;
    }

    // Find dominant tone
    const dominantTone = Object.keys(toneScores).reduce((a, b) =>
      toneScores[a] > toneScores[b] ? a : b
    );

    // If no clear tone, analyze punctuation and structure
    if (toneScores[dominantTone] === 0) {
      if (text.includes('!') || text.includes('?')) {
        return 'casual and engaging';
      } else if (text.length > 200 && text.includes('.')) {
        return 'formal and detailed';
      } else {
        return 'neutral and professional';
      }
    }

    return dominantTone;
  }

  analyzeContext(content, subject, author) {
    const text = (content + ' ' + subject).toLowerCase();

    // Context indicators
    const contexts = {
      'business inquiry': ['inquiry', 'information', 'quote', 'pricing', 'services'],
      'project discussion': ['project', 'timeline', 'milestone', 'deliverable', 'progress'],
      'meeting coordination': ['meeting', 'schedule', 'calendar', 'availability', 'time'],
      'technical support': ['issue', 'problem', 'error', 'bug', 'help', 'support'],
      'follow-up': ['follow up', 'following up', 'checking in', 'update', 'status'],
      'introduction': ['introduction', 'nice to meet', 'new', 'first time', 'pleasure'],
      'collaboration': ['collaborate', 'work together', 'partnership', 'team', 'joint']
    };

    for (const [context, indicators] of Object.entries(contexts)) {
      if (indicators.some(indicator => text.includes(indicator))) {
        return context;
      }
    }

    return 'general correspondence';
  }

  extractEmailContent(fullMessage) {
    try {
      let content = "";
      
      if (fullMessage.parts && fullMessage.parts.length > 0) {
        // Look for text/plain first
        for (const part of fullMessage.parts) {
          if (part.contentType === "text/plain" && part.body) {
            content = part.body;
            break;
          }
        }
        
        // If no plain text, try HTML and strip tags
        if (!content) {
          for (const part of fullMessage.parts) {
            if (part.contentType === "text/html" && part.body) {
              content = this.stripHtmlTags(part.body);
              break;
            }
          }
        }
      }
      
      // Fallback to message body
      if (!content && fullMessage.body) {
        content = typeof fullMessage.body === 'string' ? fullMessage.body : '';
      }
      
      // Clean and limit content length
      if (content) {
        content = content
          .replace(/\s+/g, ' ')
          .trim();
        
        // Limit to 1000 characters to avoid token limits
        if (content.length > 1000) {
          content = content.substring(0, 1000) + "...";
        }
      }
      
      return content || "No content available";
    } catch (error) {
      console.error("Error extracting email content:", error);
      return "Error extracting content";
    }
  }

  stripHtmlTags(html) {
    return html
      .replace(/<[^>]*>/g, '')
      .replace(/&nbsp;/g, ' ')
      .replace(/&amp;/g, '&')
      .replace(/&lt;/g, '<')
      .replace(/&gt;/g, '>')
      .replace(/&quot;/g, '"')
      .replace(/&#39;/g, "'")
      .trim();
  }

  async callAIAPI(model, apiKey, prompt, modelType) {
    const headers = {
      'Content-Type': 'application/json'
    };

    // Set authorization header based on model type
    if (modelType === 'deepseek') {
      headers['Authorization'] = `Bearer ${apiKey}`;
    } else if (modelType === 'openrouter') {
      headers['Authorization'] = `Bearer ${apiKey}`;
      headers['HTTP-Referer'] = 'https://thunderbird.net';
      headers['X-Title'] = 'AI Email Reply Assistant';
    }

    const requestBody = {
      model: model.model,
      messages: [
        {
          role: "user",
          content: prompt
        }
      ],
      max_tokens: model.maxTokens,
      temperature: 0.7,
      top_p: 0.9
    };

    try {
      console.log(`Making API request to ${model.endpoint}`);
      
      const response = await fetch(model.endpoint, {
        method: 'POST',
        headers: headers,
        body: JSON.stringify(requestBody)
      });

      if (!response.ok) {
        const errorText = await response.text();
        throw new Error(`API request failed: ${response.status} ${response.statusText} - ${errorText}`);
      }

      const data = await response.json();
      
      if (!data.choices || !data.choices[0] || !data.choices[0].message) {
        throw new Error('Invalid response format from AI API');
      }

      const reply = data.choices[0].message.content.trim();
      
      if (!reply) {
        throw new Error('Empty reply received from AI API');
      }

      return reply;
    } catch (error) {
      if (error.name === 'TypeError' && error.message.includes('fetch')) {
        throw new Error('Network error: Unable to connect to AI API. Please check your internet connection.');
      }
      throw error;
    }
  }

  validateApiKey(apiKey, modelType) {
    if (!apiKey || typeof apiKey !== 'string') {
      return false;
    }
    
    // Basic validation - check if key looks reasonable
    if (apiKey.length < 10) {
      return false;
    }
    
    // Model-specific validation
    if (modelType === 'deepseek') {
      return apiKey.startsWith('sk-') || apiKey.length > 20;
    } else if (modelType === 'openrouter') {
      return apiKey.startsWith('sk-') || apiKey.length > 20;
    }
    
    return true;
  }

  getAvailableModels() {
    return Object.keys(this.models).map(key => ({
      id: key,
      name: this.models[key].name,
      endpoint: this.models[key].endpoint
    }));
  }
}

// Export for use in background script
if (typeof module !== 'undefined' && module.exports) {
  module.exports = AIIntegration;
} else if (typeof window !== 'undefined') {
  window.AIIntegration = AIIntegration;
}
