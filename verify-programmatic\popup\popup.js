// AI Email Reply Assistant Popup Script

class EmailReplyAssistant {
    constructor() {
        this.emails = [];
        this.selectedEmails = new Set();
        this.config = {};
        this.init();
    }

    async init() {
        await this.loadConfig();
        this.setupEventListeners();
        this.loadEmails();
        this.updateUI();
    }

    setupEventListeners() {
        // Refresh button
        document.getElementById('refreshBtn').addEventListener('click', () => {
            this.loadEmails();
        });

        // Configuration button
        document.getElementById('configBtn').addEventListener('click', () => {
            this.showConfigModal();
        });

        // Generate replies button
        document.getElementById('generateBtn').addEventListener('click', () => {
            this.generateReplies();
        });

        // Select all button
        document.getElementById('selectAllBtn').addEventListener('click', () => {
            this.toggleSelectAll();
        });

        // Model selector
        document.getElementById('modelSelect').addEventListener('change', (e) => {
            this.config.default_model = e.target.value;
            this.saveConfig();
        });

        // Configuration modal events
        document.getElementById('closeConfigBtn').addEventListener('click', () => {
            this.hideConfigModal();
        });

        document.getElementById('saveConfigBtn').addEventListener('click', () => {
            this.saveConfigFromModal();
        });

        document.getElementById('cancelConfigBtn').addEventListener('click', () => {
            this.hideConfigModal();
        });

        // Reply modal events
        document.getElementById('closeReplyBtn').addEventListener('click', () => {
            this.hideReplyModal();
        });

        document.getElementById('copyReplyBtn').addEventListener('click', () => {
            this.copyReplyToClipboard();
        });

        document.getElementById('composeReplyBtn').addEventListener('click', () => {
            this.openInCompose();
        });

        document.getElementById('cancelReplyBtn').addEventListener('click', () => {
            this.hideReplyModal();
        });
    }

    async loadConfig() {
        try {
            const response = await browser.runtime.sendMessage({ action: 'getConfig' });
            if (response.success) {
                this.config = response.config;
                document.getElementById('modelSelect').value = this.config.default_model || 'deepseek';
            }
        } catch (error) {
            console.error('Error loading configuration:', error);
            this.showStatus('Error loading configuration', 'error');
        }
    }

    async saveConfig() {
        try {
            await browser.runtime.sendMessage({ 
                action: 'saveConfig', 
                data: this.config 
            });
        } catch (error) {
            console.error('Error saving configuration:', error);
        }
    }

    async loadEmails() {
        this.showLoading(true);
        this.showStatus('Loading emails...');

        try {
            const response = await browser.runtime.sendMessage({ action: 'getRecentEmails' });
            
            if (response.success) {
                this.emails = response.emails;
                this.renderEmails();
                this.showStatus(`Loaded ${this.emails.length} emails`);
            } else {
                this.showStatus(`Error: ${response.error}`, 'error');
            }
        } catch (error) {
            console.error('Error loading emails:', error);
            this.showStatus('Error loading emails', 'error');
        } finally {
            this.showLoading(false);
        }
    }

    renderEmails() {
        const emailList = document.getElementById('emailList');
        emailList.innerHTML = '';

        if (this.emails.length === 0) {
            emailList.innerHTML = '<div class="no-emails">No emails found</div>';
            return;
        }

        this.emails.forEach((email, index) => {
            const emailItem = this.createEmailItem(email, index);
            emailList.appendChild(emailItem);
        });

        document.getElementById('emailCount').textContent = this.emails.length;
    }

    createEmailItem(email, index) {
        const item = document.createElement('div');
        item.className = 'email-item';
        item.dataset.index = index;

        const isSelected = this.selectedEmails.has(index);
        if (isSelected) {
            item.classList.add('selected');
        }

        item.innerHTML = `
            <div class="email-header">
                <input type="checkbox" class="email-checkbox" ${isSelected ? 'checked' : ''}>
                <div class="email-subject">${this.escapeHtml(email.subject || 'No Subject')}</div>
                <div class="email-folder">${email.folder}</div>
            </div>
            <div class="email-meta">
                From: ${this.escapeHtml(email.author)} • ${this.formatDate(email.date)} • ${email.accountName}
            </div>
            <div class="email-preview">${this.escapeHtml(email.preview)}</div>
        `;

        // Add click event listener
        item.addEventListener('click', (e) => {
            if (e.target.type !== 'checkbox') {
                this.toggleEmailSelection(index);
            }
        });

        // Add checkbox event listener
        const checkbox = item.querySelector('.email-checkbox');
        checkbox.addEventListener('change', () => {
            this.toggleEmailSelection(index);
        });

        return item;
    }

    toggleEmailSelection(index) {
        if (this.selectedEmails.has(index)) {
            this.selectedEmails.delete(index);
        } else {
            this.selectedEmails.add(index);
        }
        this.updateUI();
        this.renderEmails(); // Re-render to update selection state
    }

    toggleSelectAll() {
        if (this.selectedEmails.size === this.emails.length) {
            // Deselect all
            this.selectedEmails.clear();
        } else {
            // Select all
            this.selectedEmails.clear();
            this.emails.forEach((_, index) => {
                this.selectedEmails.add(index);
            });
        }
        this.updateUI();
        this.renderEmails();
    }

    updateUI() {
        const selectedCount = this.selectedEmails.size;
        document.getElementById('selectedCount').textContent = selectedCount;
        document.getElementById('generateBtn').disabled = selectedCount === 0;
        
        const selectAllBtn = document.getElementById('selectAllBtn');
        selectAllBtn.textContent = selectedCount === this.emails.length ? 'Deselect All' : 'Select All';
    }

    async generateReplies() {
        if (this.selectedEmails.size === 0) {
            this.showStatus('Please select at least one email', 'warning');
            return;
        }

        // Validate configuration before proceeding
        if (!this.config.deepseek_api_key && !this.config.openrouter_api_key) {
            this.showStatus('Please configure your API keys first', 'error');
            this.showConfigModal();
            return;
        }

        const selectedModel = document.getElementById('modelSelect').value;

        // Check if the selected model has an API key
        if (selectedModel === 'deepseek' && !this.config.deepseek_api_key) {
            this.showStatus('DeepSeek API key not configured', 'error');
            this.showConfigModal();
            return;
        }

        if (selectedModel === 'openrouter' && !this.config.openrouter_api_key) {
            this.showStatus('OpenRouter API key not configured', 'error');
            this.showConfigModal();
            return;
        }

        this.showStatus('Generating replies...', 'info', true);
        this.showLoading(true);

        try {
            // For now, generate reply for the first selected email
            const firstSelectedIndex = Array.from(this.selectedEmails)[0];
            const email = this.emails[firstSelectedIndex];

            if (!email || !email.id) {
                throw new Error('Selected email is invalid');
            }

            const response = await browser.runtime.sendMessage({
                action: 'generateReply',
                data: {
                    emailId: email.id,
                    model: selectedModel,
                    customPrompt: null
                }
            });

            if (response.success) {
                this.showReplyModal(email, response.reply);
                this.showStatus('Reply generated successfully', 'success');
            } else {
                this.handleError(response, 'generateReplies');
            }
        } catch (error) {
            this.handleError(error, 'generateReplies');
        } finally {
            this.showLoading(false);
        }
    }

    showConfigModal() {
        document.getElementById('deepseekKey').value = this.config.deepseek_api_key || '';
        document.getElementById('openrouterKey').value = this.config.openrouter_api_key || '';
        document.getElementById('defaultModel').value = this.config.default_model || 'deepseek';
        document.getElementById('configModal').style.display = 'flex';
    }

    hideConfigModal() {
        document.getElementById('configModal').style.display = 'none';
    }

    async saveConfigFromModal() {
        this.config.deepseek_api_key = document.getElementById('deepseekKey').value;
        this.config.openrouter_api_key = document.getElementById('openrouterKey').value;
        this.config.default_model = document.getElementById('defaultModel').value;

        await this.saveConfig();
        document.getElementById('modelSelect').value = this.config.default_model;
        this.hideConfigModal();
        this.showStatus('Configuration saved');
    }

    showReplyModal(email, reply) {
        document.getElementById('originalEmailContent').innerHTML = `
            <strong>Subject:</strong> ${this.escapeHtml(email.subject)}<br>
            <strong>From:</strong> ${this.escapeHtml(email.author)}<br>
            <strong>Preview:</strong> ${this.escapeHtml(email.preview)}
        `;
        document.getElementById('replyContent').value = reply;
        document.getElementById('replyModal').style.display = 'flex';
    }

    hideReplyModal() {
        document.getElementById('replyModal').style.display = 'none';
    }

    async copyReplyToClipboard() {
        const replyText = document.getElementById('replyContent').value;
        try {
            await navigator.clipboard.writeText(replyText);
            this.showStatus('Reply copied to clipboard');
        } catch (error) {
            console.error('Error copying to clipboard:', error);
            this.showStatus('Error copying to clipboard', 'error');
        }
    }

    async openInCompose() {
        try {
            const replyText = document.getElementById('replyContent').value;

            if (!replyText.trim()) {
                this.showStatus('No reply content to compose', 'error');
                return;
            }

            // Get the original email data for the first selected email
            const firstSelectedIndex = Array.from(this.selectedEmails)[0];
            const originalEmail = this.emails[firstSelectedIndex];

            // Create compose details
            const composeDetails = {
                to: [originalEmail.author],
                subject: originalEmail.subject.startsWith('Re: ')
                    ? originalEmail.subject
                    : 'Re: ' + originalEmail.subject,
                body: replyText,
                isReply: true
            };

            // Open Thunderbird compose window
            const response = await browser.runtime.sendMessage({
                action: 'openCompose',
                data: {
                    originalMessageId: originalEmail.id,
                    composeDetails: composeDetails
                }
            });

            if (response.success) {
                this.showStatus('Compose window opened successfully');
                this.hideReplyModal();
            } else {
                this.showStatus(`Error opening compose window: ${response.error}`, 'error');
            }
        } catch (error) {
            console.error('Error opening compose window:', error);
            this.showStatus('Error opening compose window', 'error');
        }
    }

    showLoading(show) {
        document.getElementById('loadingIndicator').style.display = show ? 'block' : 'none';
    }

    showStatus(message, type = 'info', persistent = false) {
        const statusBar = document.getElementById('statusBar');
        statusBar.textContent = message;
        statusBar.className = `status-bar ${type}`;

        // Add visual feedback for different types
        if (type === 'error') {
            statusBar.style.backgroundColor = '#dc3545';
            statusBar.style.color = 'white';
        } else if (type === 'success') {
            statusBar.style.backgroundColor = '#28a745';
            statusBar.style.color = 'white';
        } else if (type === 'warning') {
            statusBar.style.backgroundColor = '#ffc107';
            statusBar.style.color = 'black';
        } else {
            statusBar.style.backgroundColor = '#e9ecef';
            statusBar.style.color = '#6c757d';
        }

        // Clear status after delay unless persistent
        if (!persistent) {
            setTimeout(() => {
                statusBar.textContent = 'Ready';
                statusBar.className = 'status-bar';
                statusBar.style.backgroundColor = '#e9ecef';
                statusBar.style.color = '#6c757d';
            }, type === 'error' ? 5000 : 3000); // Show errors longer
        }
    }

    handleError(error, context = '') {
        console.error(`Error in ${context}:`, error);

        let errorMessage = 'An unexpected error occurred';

        if (error && error.details) {
            // Use formatted error from error handler
            errorMessage = error.details.message;

            // Show suggestions if available
            if (error.details.suggestions && error.details.suggestions.length > 0) {
                console.log('Suggestions:', error.details.suggestions);
            }
        } else if (error && error.message) {
            errorMessage = error.message;
        }

        this.showStatus(errorMessage, 'error');
        return errorMessage;
    }

    escapeHtml(text) {
        const div = document.createElement('div');
        div.textContent = text;
        return div.innerHTML;
    }

    formatDate(dateString) {
        const date = new Date(dateString);
        return date.toLocaleDateString() + ' ' + date.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' });
    }
}

// Initialize the application when the DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
    new EmailReplyAssistant();
});
