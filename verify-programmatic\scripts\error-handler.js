// Error <PERSON> and Validation Module for AI Email Reply Assistant

class ErrorHandler {
  constructor() {
    this.errorTypes = {
      NETWORK_ERROR: 'network',
      API_ERROR: 'api',
      CONFIG_ERROR: 'config',
      VALIDATION_ERROR: 'validation',
      PERMISSION_ERROR: 'permission',
      UNKNOWN_ERROR: 'unknown'
    };

    this.errorMessages = {
      network: {
        title: 'Network Error',
        message: 'Unable to connect to the service. Please check your internet connection.',
        suggestions: ['Check your internet connection', 'Try again in a few moments', 'Verify firewall settings']
      },
      api: {
        title: 'API Error',
        message: 'There was an error communicating with the AI service.',
        suggestions: ['Verify your API key is correct', 'Check if the service is available', 'Try a different AI model']
      },
      config: {
        title: 'Configuration Error',
        message: 'There is an issue with your configuration.',
        suggestions: ['Check your API keys', 'Verify configuration format', 'Reset to default settings']
      },
      validation: {
        title: 'Validation Error',
        message: 'The provided data is invalid.',
        suggestions: ['Check your input', 'Ensure all required fields are filled', 'Verify data format']
      },
      permission: {
        title: 'Permission Error',
        message: 'The addon does not have the required permissions.',
        suggestions: ['Check addon permissions', 'Reinstall the addon', 'Contact support']
      },
      unknown: {
        title: 'Unknown Error',
        message: 'An unexpected error occurred.',
        suggestions: ['Try again', 'Restart Thunderbird', 'Check the console for details']
      }
    };
  }

  handleError(error, context = '') {
    console.error(`Error in ${context}:`, error);
    
    const errorInfo = this.categorizeError(error);
    const userFriendlyError = this.formatErrorForUser(errorInfo, context);
    
    // Log detailed error for debugging
    this.logError(error, context, errorInfo);
    
    return userFriendlyError;
  }

  categorizeError(error) {
    if (!error) {
      return { type: this.errorTypes.UNKNOWN_ERROR, originalError: error };
    }

    const errorMessage = error.message || error.toString();
    const errorLower = errorMessage.toLowerCase();

    // Network errors
    if (errorLower.includes('network') || 
        errorLower.includes('fetch') || 
        errorLower.includes('connection') ||
        errorLower.includes('timeout') ||
        error.name === 'TypeError' && errorLower.includes('fetch')) {
      return { type: this.errorTypes.NETWORK_ERROR, originalError: error };
    }

    // API errors
    if (errorLower.includes('api') || 
        errorLower.includes('unauthorized') || 
        errorLower.includes('forbidden') ||
        errorLower.includes('rate limit') ||
        errorLower.includes('quota') ||
        error.status >= 400 && error.status < 500) {
      return { type: this.errorTypes.API_ERROR, originalError: error };
    }

    // Configuration errors
    if (errorLower.includes('config') || 
        errorLower.includes('api key') || 
        errorLower.includes('invalid key') ||
        errorLower.includes('authentication')) {
      return { type: this.errorTypes.CONFIG_ERROR, originalError: error };
    }

    // Validation errors
    if (errorLower.includes('validation') || 
        errorLower.includes('invalid') || 
        errorLower.includes('required') ||
        errorLower.includes('format')) {
      return { type: this.errorTypes.VALIDATION_ERROR, originalError: error };
    }

    // Permission errors
    if (errorLower.includes('permission') || 
        errorLower.includes('access denied') || 
        errorLower.includes('not allowed')) {
      return { type: this.errorTypes.PERMISSION_ERROR, originalError: error };
    }

    return { type: this.errorTypes.UNKNOWN_ERROR, originalError: error };
  }

  formatErrorForUser(errorInfo, context) {
    const errorTemplate = this.errorMessages[errorInfo.type] || this.errorMessages.unknown;
    
    return {
      type: errorInfo.type,
      title: errorTemplate.title,
      message: errorTemplate.message,
      suggestions: errorTemplate.suggestions,
      context: context,
      timestamp: new Date().toISOString(),
      details: this.sanitizeErrorDetails(errorInfo.originalError)
    };
  }

  sanitizeErrorDetails(error) {
    if (!error) return 'No error details available';
    
    // Remove sensitive information from error messages
    let details = error.message || error.toString();
    
    // Remove API keys or tokens
    details = details.replace(/sk-[a-zA-Z0-9]+/g, 'sk-***');
    details = details.replace(/Bearer [a-zA-Z0-9]+/g, 'Bearer ***');
    
    return details;
  }

  logError(error, context, errorInfo) {
    const logEntry = {
      timestamp: new Date().toISOString(),
      context: context,
      type: errorInfo.type,
      message: error.message || error.toString(),
      stack: error.stack,
      userAgent: navigator.userAgent
    };

    // In a production environment, you might want to send this to a logging service
    console.error('Detailed error log:', logEntry);
  }

  validateApiKey(apiKey, modelType) {
    const errors = [];

    if (!apiKey) {
      errors.push('API key is required');
      return { valid: false, errors };
    }

    if (typeof apiKey !== 'string') {
      errors.push('API key must be a string');
      return { valid: false, errors };
    }

    if (apiKey.length < 10) {
      errors.push('API key appears to be too short');
    }

    // Model-specific validation
    if (modelType === 'deepseek') {
      if (!apiKey.startsWith('sk-') && apiKey.length < 20) {
        errors.push('DeepSeek API key format appears invalid');
      }
    } else if (modelType === 'openrouter') {
      if (!apiKey.startsWith('sk-') && apiKey.length < 20) {
        errors.push('OpenRouter API key format appears invalid');
      }
    }

    return {
      valid: errors.length === 0,
      errors: errors
    };
  }

  validateEmailData(emailData) {
    const errors = [];

    if (!emailData) {
      errors.push('Email data is required');
      return { valid: false, errors };
    }

    if (!emailData.id) {
      errors.push('Email ID is required');
    }

    if (!emailData.subject && !emailData.preview) {
      errors.push('Email must have either a subject or preview content');
    }

    if (!emailData.author) {
      errors.push('Email author is required');
    }

    return {
      valid: errors.length === 0,
      errors: errors
    };
  }

  validateConfiguration(config) {
    const errors = [];

    if (!config) {
      errors.push('Configuration is required');
      return { valid: false, errors };
    }

    if (!config.deepseek_api_key && !config.openrouter_api_key) {
      errors.push('At least one API key must be provided');
    }

    if (config.deepseek_api_key) {
      const deepseekValidation = this.validateApiKey(config.deepseek_api_key, 'deepseek');
      if (!deepseekValidation.valid) {
        errors.push(...deepseekValidation.errors.map(e => `DeepSeek: ${e}`));
      }
    }

    if (config.openrouter_api_key) {
      const openrouterValidation = this.validateApiKey(config.openrouter_api_key, 'openrouter');
      if (!openrouterValidation.valid) {
        errors.push(...openrouterValidation.errors.map(e => `OpenRouter: ${e}`));
      }
    }

    if (config.default_model && !['deepseek', 'openrouter'].includes(config.default_model)) {
      errors.push('Default model must be either "deepseek" or "openrouter"');
    }

    return {
      valid: errors.length === 0,
      errors: errors
    };
  }

  createUserNotification(errorInfo) {
    return {
      type: 'error',
      title: errorInfo.title,
      message: errorInfo.message,
      actions: errorInfo.suggestions.map((suggestion, index) => ({
        id: `suggestion_${index}`,
        title: suggestion
      }))
    };
  }

  async showNotification(errorInfo) {
    try {
      if (browser.notifications) {
        const notification = this.createUserNotification(errorInfo);
        await browser.notifications.create({
          type: 'basic',
          iconUrl: 'icons/icon-48.png',
          title: notification.title,
          message: notification.message
        });
      }
    } catch (error) {
      console.error('Failed to show notification:', error);
    }
  }
}

// Export for use in other modules
if (typeof module !== 'undefined' && module.exports) {
  module.exports = ErrorHandler;
} else if (typeof window !== 'undefined') {
  window.ErrorHandler = ErrorHandler;
}
