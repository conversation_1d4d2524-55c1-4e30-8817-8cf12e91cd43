# Build script for AI Email Reply Assistant Thunderbird Addon
# This script creates an XPI package for installation

Write-Host "Building AI Email Reply Assistant XPI package..." -ForegroundColor Green

# Define the addon name and version
$addonName = "ai-email-reply-assistant"
$version = "1.0.0"
$xpiFileName = "$addonName-$version.xpi"

# Create a temporary build directory
$buildDir = "build-temp"
if (Test-Path $buildDir) {
    Remove-Item $buildDir -Recurse -Force
}
New-Item -ItemType Directory -Path $buildDir | Out-Null

Write-Host "Created temporary build directory: $buildDir" -ForegroundColor Yellow

# List of files and directories to include in the XPI
$filesToInclude = @(
    "manifest.json",
    "background.js",
    "popup",
    "scripts", 
    "styles",
    "icons"
)

# Copy files to build directory
Write-Host "Copying addon files..." -ForegroundColor Yellow
foreach ($item in $filesToInclude) {
    if (Test-Path $item) {
        if (Test-Path $item -PathType Container) {
            # It's a directory
            Copy-Item $item -Destination $buildDir -Recurse
            Write-Host "  Copied directory: $item" -ForegroundColor Gray
        } else {
            # It's a file
            Copy-Item $item -Destination $buildDir
            Write-Host "  Copied file: $item" -ForegroundColor Gray
        }
    } else {
        Write-Host "  Warning: $item not found, skipping..." -ForegroundColor Red
    }
}

# Create placeholder icons if they don't exist
$iconsDir = Join-Path $buildDir "icons"
if (-not (Test-Path $iconsDir)) {
    New-Item -ItemType Directory -Path $iconsDir | Out-Null
}

$iconSizes = @(16, 32, 48, 64, 128)
foreach ($size in $iconSizes) {
    $iconFile = Join-Path $iconsDir "icon-$size.png"
    if (-not (Test-Path $iconFile)) {
        # Create a simple placeholder icon using PowerShell
        # This creates a minimal PNG file - in production, use proper icons
        Write-Host "  Creating placeholder icon: icon-$size.png" -ForegroundColor Gray
        
        # Create a simple SVG and note for manual icon creation
        $svgContent = @"
<svg width="$size" height="$size" xmlns="http://www.w3.org/2000/svg">
  <rect width="$size" height="$size" fill="#007bff" rx="4"/>
  <text x="50%" y="50%" text-anchor="middle" dy=".3em" fill="white" font-family="Arial" font-size="$(($size/3))">AI</text>
</svg>
"@
        $svgFile = Join-Path $iconsDir "icon-$size.svg"
        $svgContent | Out-File -FilePath $svgFile -Encoding UTF8
        
        # Note: For production, convert SVG to PNG or create proper PNG icons
        # For now, we'll include the SVG files as placeholders
    }
}

# Create the XPI file (which is just a ZIP file with .xpi extension)
Write-Host "Creating XPI package..." -ForegroundColor Yellow

# Remove existing XPI if it exists
if (Test-Path $xpiFileName) {
    Remove-Item $xpiFileName -Force
    Write-Host "  Removed existing $xpiFileName" -ForegroundColor Gray
}

# Create ZIP archive first, then rename to XPI
$tempZipFile = "$addonName-$version.zip"
try {
    # Change to build directory to ensure proper file structure in ZIP
    Push-Location $buildDir

    # Create the ZIP file first
    Compress-Archive -Path * -DestinationPath "..\$tempZipFile" -CompressionLevel Optimal

    Pop-Location

    # Rename ZIP to XPI
    Move-Item $tempZipFile $xpiFileName

    Write-Host "Successfully created: $xpiFileName" -ForegroundColor Green

    # Get file size
    $fileSize = (Get-Item $xpiFileName).Length
    $fileSizeKB = [math]::Round($fileSize / 1024, 2)
    Write-Host "  File size: $fileSizeKB KB" -ForegroundColor Gray

} catch {
    Pop-Location
    Write-Host "Error creating XPI package: $($_.Exception.Message)" -ForegroundColor Red
    exit 1
}

# Clean up build directory
Write-Host "Cleaning up..." -ForegroundColor Yellow
Remove-Item $buildDir -Recurse -Force

# Verify the XPI file
Write-Host "Verifying XPI package..." -ForegroundColor Yellow
if (Test-Path $xpiFileName) {
    # List contents of the XPI file
    Write-Host "XPI Contents:" -ForegroundColor Cyan
    Add-Type -AssemblyName System.IO.Compression.FileSystem
    $zip = [System.IO.Compression.ZipFile]::OpenRead((Resolve-Path $xpiFileName))
    foreach ($entry in $zip.Entries) {
        Write-Host "  $($entry.FullName)" -ForegroundColor Gray
    }
    $zip.Dispose()
    
    Write-Host "`nXPI package created successfully!" -ForegroundColor Green
    Write-Host "File: $xpiFileName" -ForegroundColor Cyan
    Write-Host "`nInstallation Instructions:" -ForegroundColor Yellow
    Write-Host "1. Open Thunderbird" -ForegroundColor White
    Write-Host "2. Go to Tools > Add-ons and Themes" -ForegroundColor White
    Write-Host "3. Click the gear icon and select 'Install Add-on From File'" -ForegroundColor White
    Write-Host "4. Select the file: $xpiFileName" -ForegroundColor White
    Write-Host "5. Click 'Add' to install the addon" -ForegroundColor White
    
} else {
    Write-Host "Error: XPI file was not created!" -ForegroundColor Red
    exit 1
}

Write-Host "`nBuild completed successfully!" -ForegroundColor Green
