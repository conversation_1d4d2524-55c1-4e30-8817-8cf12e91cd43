// Simple background script for AI Email Reply Assistant
console.log("AI Email Reply Assistant background script loaded");

// Browser action click handler - open popup window
browser.browserAction.onClicked.addListener(async () => {
  try {
    console.log("Browser action clicked, opening popup window");
    const window = await browser.windows.create({
      url: "popup/popup-simple.html",
      type: "popup",
      height: 600,
      width: 400,
      allowScriptsToClose: true
    });
    console.log("Popup window created:", window.id);
  } catch (error) {
    console.error("Error opening popup window:", error);
  }
});

console.log("Background script setup complete");
