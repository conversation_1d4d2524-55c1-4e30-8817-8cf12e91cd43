# Simple build script for AI Email Reply Assistant
Write-Host "Building AI Email Reply Assistant XPI package..."

$buildDir = "simple-build"
$xpiName = "ai-email-reply-simple.xpi"

# Clean up previous build
if (Test-Path $buildDir) { Remove-Item $buildDir -Recurse -Force }
if (Test-Path $xpiName) { Remove-Item $xpiName -Force }

# Create build directory
New-Item -ItemType Directory -Path $buildDir | Out-Null

# Copy files
Copy-Item "manifest.json" -Destination $buildDir
Copy-Item "background.js" -Destination $buildDir
Copy-Item "popup" -Destination $buildDir -Recurse
Copy-Item "scripts" -Destination $buildDir -Recurse
Copy-Item "styles" -Destination $buildDir -Recurse
Copy-Item "icons" -Destination $buildDir -Recurse

Write-Host "Files copied to build directory"

# Create XPI
Push-Location $buildDir
$tempZip = "../temp-build.zip"
Compress-Archive -Path * -DestinationPath $tempZip -CompressionLevel Optimal -Force
Pop-Location

# Rename to XPI
Move-Item "temp-build.zip" $xpiName

# Cleanup
Remove-Item $buildDir -Recurse -Force

Write-Host "XPI created: $xpiName"

# Verify contents
$verifyDir = "verify-contents"
if (Test-Path $verifyDir) { Remove-Item $verifyDir -Recurse -Force }
Expand-Archive -Path $xpiName -DestinationPath $verifyDir

Write-Host "XPI Contents:"
Get-ChildItem -Path $verifyDir -Recurse | ForEach-Object { 
    $relativePath = $_.FullName.Replace((Get-Item $verifyDir).FullName, "").TrimStart("\")
    if ($_.PSIsContainer) {
        Write-Host "  $relativePath/"
    } else {
        Write-Host "  $relativePath"
    }
}

# Check manifest
Write-Host "`nManifest popup setting:"
$manifest = Get-Content "$verifyDir/manifest.json" | ConvertFrom-Json
Write-Host "  default_popup: $($manifest.browser_action.default_popup)"

# Cleanup verification
Remove-Item $verifyDir -Recurse -Force

Write-Host "`nBuild completed successfully!"
