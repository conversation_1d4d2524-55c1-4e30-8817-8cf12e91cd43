// Hello World V3 background script
console.log("Hello World V3 extension loaded");

// Action click handler (V3 uses 'action' instead of 'browserAction')
browser.action.onClicked.addListener(async () => {
  console.log("Hello World V3 button clicked!");
  
  // Try to open a simple popup window
  try {
    const window = await browser.windows.create({
      url: "data:text/html,<html><body><h1>Hello World V3!</h1><p>Manifest V3 extension is working!</p></body></html>",
      type: "popup",
      height: 200,
      width: 300,
      allowScriptsToClose: true
    });
    console.log("V3 Popup window created:", window.id);
  } catch (error) {
    console.error("Error opening V3 popup window:", error);
  }
});
