// Configuration Manager for AI Email Reply Assistant

class ConfigManager {
  constructor() {
    this.defaultConfig = {
      deepseek_api_key: "",
      openrouter_api_key: "",
      default_model: "deepseek",
      models: {
        deepseek: {
          name: "DeepSeek Chat",
          endpoint: "https://api.deepseek.com/v1/chat/completions",
          model: "deepseek-chat",
          enabled: true
        },
        openrouter: {
          name: "OpenRouter GPT-4",
          endpoint: "https://openrouter.ai/api/v1/chat/completions",
          model: "openai/gpt-4",
          enabled: true
        }
      },
      ui_preferences: {
        auto_refresh: true,
        show_previews: true,
        max_emails: 20
      }
    };
  }

  async loadConfig() {
    try {
      // First try to load from browser storage
      const result = await browser.storage.local.get("aiConfig");
      let config = result.aiConfig || {};

      // Try to load from ai_keys.json file if storage is empty
      if (!config.deepseek_api_key && !config.openrouter_api_key) {
        const fileConfig = await this.loadConfigFromFile();
        if (fileConfig) {
          config = { ...config, ...fileConfig };
          await this.saveConfig(config);
        }
      }

      // Merge with default config to ensure all properties exist
      config = this.mergeWithDefaults(config);
      
      return {
        success: true,
        config: config
      };
    } catch (error) {
      console.error("Error loading configuration:", error);
      return {
        success: false,
        error: error.message,
        config: this.defaultConfig
      };
    }
  }

  async loadConfigFromFile() {
    try {
      // Note: In a real Thunderbird extension, you would need to use the file API
      // or have the user manually input the configuration
      // For now, we'll return null and rely on manual configuration
      console.log("File-based configuration loading not implemented in this version");
      return null;
    } catch (error) {
      console.error("Error loading config from file:", error);
      return null;
    }
  }

  async saveConfig(config) {
    try {
      // Validate configuration before saving
      const validationResult = this.validateConfig(config);
      if (!validationResult.valid) {
        throw new Error(`Configuration validation failed: ${validationResult.errors.join(', ')}`);
      }

      // Save to browser storage
      await browser.storage.local.set({ aiConfig: config });
      
      console.log("Configuration saved successfully");
      return { success: true };
    } catch (error) {
      console.error("Error saving configuration:", error);
      return { 
        success: false, 
        error: error.message 
      };
    }
  }

  validateConfig(config) {
    const errors = [];
    
    try {
      // Check if config is an object
      if (!config || typeof config !== 'object') {
        errors.push("Configuration must be an object");
        return { valid: false, errors };
      }

      // Validate API keys format
      if (config.deepseek_api_key && typeof config.deepseek_api_key !== 'string') {
        errors.push("DeepSeek API key must be a string");
      }

      if (config.openrouter_api_key && typeof config.openrouter_api_key !== 'string') {
        errors.push("OpenRouter API key must be a string");
      }

      // Check that at least one API key is provided
      if (!config.deepseek_api_key && !config.openrouter_api_key) {
        errors.push("At least one API key must be provided");
      }

      // Validate default model
      if (config.default_model && !['deepseek', 'openrouter'].includes(config.default_model)) {
        errors.push("Default model must be either 'deepseek' or 'openrouter'");
      }

      // Validate API key formats (basic check)
      if (config.deepseek_api_key && config.deepseek_api_key.length < 10) {
        errors.push("DeepSeek API key appears to be too short");
      }

      if (config.openrouter_api_key && config.openrouter_api_key.length < 10) {
        errors.push("OpenRouter API key appears to be too short");
      }

      return {
        valid: errors.length === 0,
        errors: errors
      };
    } catch (error) {
      console.error("Error during config validation:", error);
      return {
        valid: false,
        errors: ["Configuration validation failed: " + error.message]
      };
    }
  }

  mergeWithDefaults(config) {
    try {
      return {
        ...this.defaultConfig,
        ...config,
        models: {
          ...this.defaultConfig.models,
          ...(config.models || {})
        },
        ui_preferences: {
          ...this.defaultConfig.ui_preferences,
          ...(config.ui_preferences || {})
        }
      };
    } catch (error) {
      console.error("Error merging config with defaults:", error);
      return this.defaultConfig;
    }
  }

  async resetConfig() {
    try {
      await browser.storage.local.set({ aiConfig: this.defaultConfig });
      return { success: true };
    } catch (error) {
      console.error("Error resetting configuration:", error);
      return { 
        success: false, 
        error: error.message 
      };
    }
  }

  async exportConfig() {
    try {
      const result = await this.loadConfig();
      if (result.success) {
        // Remove sensitive data for export
        const exportConfig = { ...result.config };
        exportConfig.deepseek_api_key = exportConfig.deepseek_api_key ? "***HIDDEN***" : "";
        exportConfig.openrouter_api_key = exportConfig.openrouter_api_key ? "***HIDDEN***" : "";
        
        return {
          success: true,
          config: exportConfig
        };
      } else {
        return result;
      }
    } catch (error) {
      console.error("Error exporting configuration:", error);
      return {
        success: false,
        error: error.message
      };
    }
  }

  getConfigTemplate() {
    return {
      deepseek_api_key: "your-deepseek-api-key-here",
      openrouter_api_key: "your-openrouter-api-key-here",
      default_model: "deepseek",
      models: {
        deepseek: {
          name: "DeepSeek Chat",
          endpoint: "https://api.deepseek.com/v1/chat/completions",
          model: "deepseek-chat",
          enabled: true
        },
        openrouter: {
          name: "OpenRouter GPT-4", 
          endpoint: "https://openrouter.ai/api/v1/chat/completions",
          model: "openai/gpt-4",
          enabled: true
        }
      }
    };
  }

  async testApiKey(apiKey, modelType) {
    try {
      if (!apiKey || !modelType) {
        return {
          success: false,
          error: "API key and model type are required"
        };
      }

      // Basic format validation
      if (typeof apiKey !== 'string' || apiKey.length < 10) {
        return {
          success: false,
          error: "API key format appears invalid"
        };
      }

      // For now, just return success for basic validation
      // In a full implementation, you might make a test API call
      return {
        success: true,
        message: "API key format appears valid"
      };
    } catch (error) {
      console.error("Error testing API key:", error);
      return {
        success: false,
        error: error.message
      };
    }
  }
}

// Export for use in background script
if (typeof module !== 'undefined' && module.exports) {
  module.exports = ConfigManager;
} else if (typeof window !== 'undefined') {
  window.ConfigManager = ConfigManager;
}
