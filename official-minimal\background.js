// Hello World background script
console.log("Hello World extension loaded");

// Browser action click handler
browser.browserAction.onClicked.addListener(async () => {
  console.log("Hello World button clicked!");
  
  // Show a simple alert
  console.log("Hello World - Extension is working!");

  // Try to open a simple popup window
  try {
    const window = await browser.windows.create({
      url: "data:text/html,<html><body><h1>Hello World!</h1><p>Extension is working!</p></body></html>",
      type: "popup",
      height: 200,
      width: 300,
      allowScriptsToClose: true
    });
    console.log("Popup window created:", window.id);
  } catch (error) {
    console.error("Error opening popup window:", error);
  }
});
