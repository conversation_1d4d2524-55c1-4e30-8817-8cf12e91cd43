#!/usr/bin/env pwsh

$buildDir = 'working-build'
$xpiName = 'ai-email-reply-working.xpi'

Write-Host "Building working AI Email Reply XPI..."

# Clean up
if (Test-Path $buildDir) { Remove-Item $buildDir -Recurse -Force }
if (Test-Path $xpiName) { Remove-Item $xpiName -Force }

# Create build directory
New-Item -ItemType Directory -Path $buildDir | Out-Null

# Copy essential files only
Copy-Item 'manifest.json' -Destination $buildDir
Copy-Item 'popup-ai.html' -Destination $buildDir

Write-Host "Files copied to build directory"

# Create ZIP
Push-Location $buildDir
Compress-Archive -Path * -DestinationPath '../temp-working.zip' -CompressionLevel Optimal -Force
Pop-Location

# Rename to XPI
Move-Item 'temp-working.zip' $xpiName

# Clean up
Remove-Item $buildDir -Recurse -Force

Write-Host "Working AI Email Reply XPI created: $xpiName"
