<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>AI Email Reply Assistant</title>
    <link rel="stylesheet" href="../styles/popup.css">
</head>
<body>
    <div class="container">
        <header class="header">
            <h1>🤖 AI Email Reply Assistant</h1>
            <button id="refreshBtn" class="refresh-btn" title="Refresh emails">🔄</button>
        </header>

        <div class="config-section">
            <div class="model-selector">
                <label for="modelSelect">AI Model:</label>
                <select id="modelSelect">
                    <option value="deepseek">DeepSeek Chat</option>
                    <option value="openrouter">OpenRouter GPT-4</option>
                </select>
            </div>
            <button id="configBtn" class="config-btn">⚙️ Settings</button>
        </div>

        <div class="loading" id="loadingIndicator" style="display: none;">
            <div class="spinner"></div>
            <span>Loading emails...</span>
        </div>

        <div class="email-section">
            <div class="section-header">
                <h2>Recent Emails</h2>
                <div class="email-count">
                    <span id="emailCount">0</span> emails found
                </div>
            </div>
            
            <div class="email-list" id="emailList">
                <!-- Emails will be populated here -->
            </div>
        </div>

        <div class="action-section">
            <div class="selected-count">
                <span id="selectedCount">0</span> emails selected
            </div>
            <div class="action-buttons">
                <button id="generateBtn" class="generate-btn" disabled>
                    ✨ Generate Replies
                </button>
                <button id="selectAllBtn" class="select-all-btn">
                    Select All
                </button>
            </div>
        </div>

        <div class="status-bar" id="statusBar">
            Ready
        </div>
    </div>

    <!-- Configuration Modal -->
    <div id="configModal" class="modal" style="display: none;">
        <div class="modal-content">
            <div class="modal-header">
                <h2>Configuration</h2>
                <button class="close-btn" id="closeConfigBtn">&times;</button>
            </div>
            <div class="modal-body">
                <div class="config-group">
                    <label for="deepseekKey">DeepSeek API Key:</label>
                    <input type="password" id="deepseekKey" placeholder="Enter your DeepSeek API key">
                </div>
                <div class="config-group">
                    <label for="openrouterKey">OpenRouter API Key:</label>
                    <input type="password" id="openrouterKey" placeholder="Enter your OpenRouter API key">
                </div>
                <div class="config-group">
                    <label for="defaultModel">Default Model:</label>
                    <select id="defaultModel">
                        <option value="deepseek">DeepSeek Chat</option>
                        <option value="openrouter">OpenRouter GPT-4</option>
                    </select>
                </div>
            </div>
            <div class="modal-footer">
                <button id="saveConfigBtn" class="save-btn">Save Configuration</button>
                <button id="cancelConfigBtn" class="cancel-btn">Cancel</button>
            </div>
        </div>
    </div>

    <!-- Reply Preview Modal -->
    <div id="replyModal" class="modal" style="display: none;">
        <div class="modal-content reply-modal">
            <div class="modal-header">
                <h2>Generated Reply Preview</h2>
                <button class="close-btn" id="closeReplyBtn">&times;</button>
            </div>
            <div class="modal-body">
                <div class="original-email">
                    <h3>Original Email:</h3>
                    <div id="originalEmailContent"></div>
                </div>
                <div class="generated-reply">
                    <h3>Generated Reply:</h3>
                    <textarea id="replyContent" rows="10"></textarea>
                </div>
            </div>
            <div class="modal-footer">
                <button id="copyReplyBtn" class="copy-btn">📋 Copy to Clipboard</button>
                <button id="composeReplyBtn" class="compose-btn">✉️ Open in Compose</button>
                <button id="cancelReplyBtn" class="cancel-btn">Cancel</button>
            </div>
        </div>
    </div>

    <script src="popup.js"></script>
</body>
</html>
