<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <title>Simple Popup Test</title>
    <style>
        body {
            width: 300px;
            height: 200px;
            padding: 20px;
            font-family: Arial, sans-serif;
            background: #f0f0f0;
        }
        h1 {
            color: #333;
            font-size: 18px;
            margin: 0 0 10px 0;
        }
        p {
            color: #666;
            font-size: 14px;
            margin: 10px 0;
        }
        button {
            background: #0078d4;
            color: white;
            border: none;
            padding: 8px 16px;
            border-radius: 4px;
            cursor: pointer;
            font-size: 14px;
        }
        button:hover {
            background: #106ebe;
        }
    </style>
</head>
<body>
    <h1>Simple Popup Test</h1>
    <p>This popup uses default_popup approach.</p>
    <p>If you see this, the popup is working!</p>
    <button onclick="testFunction()">Test Button</button>
    
    <script>
        function testFunction() {
            alert('Popup JavaScript is working!');
        }
        console.log('Simple popup loaded successfully');
    </script>
</body>
</html>
