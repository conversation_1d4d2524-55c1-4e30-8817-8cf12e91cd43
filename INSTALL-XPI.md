# Quick XPI Installation Guide

## 📦 Installing AI Email Reply Assistant XPI

### Prerequisites
- Thunderbird 78.0 or later
- The XPI file: `ai-email-reply-assistant-1.0.0.xpi`

### Installation Steps

1. **Download the XPI File**
   - Ensure you have `ai-email-reply-assistant-1.0.0.xpi` (21.92 KB)
   - If you don't have it, run the build script to create it

2. **Open Thunderbird**
   - Launch Thunderbird application

3. **Access Add-ons Manager**
   - Go to `Tools` → `Add-ons and Themes`
   - Or use keyboard shortcut: `Ctrl+Shift+A` (Windows/Linux) or `Cmd+Shift+A` (macOS)

4. **Install from File**
   - Click the gear/settings icon (⚙️) in the top-right corner
   - Select `Install Add-on From File...`
   - Browse to and select `ai-email-reply-assistant-1.0.0.xpi`
   - Click `Open`

5. **Confirm Installation**
   - A dialog will appear asking to add the extension
   - Click `Add` to confirm
   - The addon will be installed and activated

6. **Verify Installation**
   - Look for the AI Email Reply Assistant button in the toolbar
   - If not visible, right-click toolbar → `Customize` → drag the button to toolbar
   - The addon should appear in your add-ons list as "AI Email Reply Assistant v1.0.0"

### Post-Installation Setup

1. **Configure API Keys**
   - Click the AI Email Reply Assistant toolbar button
   - Click the `⚙️ Settings` button in the popup
   - Enter your DeepSeek and/or OpenRouter API keys
   - Click `Save Configuration`

2. **Test Functionality**
   - The popup should show recent emails from your accounts
   - Try selecting an email and generating a reply
   - Verify the AI response appears in the preview modal

### Troubleshooting

#### "This add-on could not be installed because it appears to be corrupt"
- **Solution**: Re-download or rebuild the XPI file
- Ensure the file size is approximately 21.92 KB

#### "Add-on is not compatible with this version of Thunderbird"
- **Solution**: Check Thunderbird version (must be 78.0+)
- Update Thunderbird if necessary

#### "Installation failed"
- **Solution**: 
  - Try restarting Thunderbird
  - Check if you have sufficient permissions
  - Temporarily disable antivirus during installation

#### Toolbar button not visible
- **Solution**:
  - Right-click on toolbar
  - Select `Customize`
  - Drag "AI Email Reply Assistant" button to toolbar
  - Click `Done`

### Building XPI from Source

If you need to build the XPI file yourself:

#### Windows:
```powershell
.\build.ps1
```

#### Linux/macOS:
```bash
chmod +x build.sh
./build.sh
```

Both scripts will create `ai-email-reply-assistant-1.0.0.xpi` in the current directory.

### XPI File Contents

The XPI package includes:
- `manifest.json` - Extension configuration
- `background.js` - Main background script
- `popup/` - User interface files
- `scripts/` - AI integration and utility modules
- `styles/` - CSS styling
- `icons/` - Extension icons (SVG placeholders)

### Security Notes

- The XPI file is a standard ZIP archive with extension metadata
- All code is open source and can be inspected
- API keys are stored locally in Thunderbird's secure storage
- No data is transmitted except to selected AI providers

### Uninstallation

To remove the addon:
1. Go to `Tools` → `Add-ons and Themes`
2. Find "AI Email Reply Assistant"
3. Click the three dots menu → `Remove`
4. Restart Thunderbird if prompted

### Support

If you encounter issues:
1. Check the Error Console: `Tools` → `Developer Tools` → `Error Console`
2. Look for messages starting with "AI Email Reply Assistant"
3. Report issues with console output and system details

---

**File**: `ai-email-reply-assistant-1.0.0.xpi`  
**Size**: ~22 KB  
**Version**: 1.0.0  
**Compatibility**: Thunderbird 78.0+
