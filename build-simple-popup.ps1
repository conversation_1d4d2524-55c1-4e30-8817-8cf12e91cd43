#!/usr/bin/env pwsh

$xpiName = 'simple-popup-test.xpi'

Write-Host "Building simple popup test XPI..."

if (Test-Path $xpiName) { Remove-Item $xpiName -Force }

Push-Location 'simple-popup-test'
Compress-Archive -Path * -DestinationPath "../temp-simple-popup.zip" -CompressionLevel Optimal -Force
Pop-Location

Move-Item 'temp-simple-popup.zip' $xpiName
Write-Host "Simple popup test XPI created: $xpiName"
