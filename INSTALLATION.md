# Installation Guide - AI Email Reply Assistant

This guide provides detailed instructions for installing and configuring the AI Email Reply Assistant for Thunderbird.

## Prerequisites

### System Requirements
- **Thunderbird**: Version 78.0 or later
- **Operating System**: Windows, macOS, or Linux
- **Internet Connection**: Required for AI API calls
- **Disk Space**: ~5MB for the addon

### API Requirements
You need at least one of the following:
- **DeepSeek API Key**: From [platform.deepseek.com](https://platform.deepseek.com/)
- **OpenRouter API Key**: From [openrouter.ai](https://openrouter.ai/)

## Step-by-Step Installation

### Step 1: Download the Addon

#### Option A: Download Pre-built XPI (Recommended)
1. Download the latest release: `ai-email-reply-assistant-1.0.0.xpi`
2. Save the XPI file to your computer

#### Option B: Build from Source
```bash
git clone https://github.com/your-repo/ai-email-reply-assistant.git
cd ai-email-reply-assistant

# On Windows:
.\build.ps1

# On Linux/macOS:
./build.sh
```

This will create `ai-email-reply-assistant-1.0.0.xpi` file.

### Step 2: Install in Thunderbird

1. **Open Thunderbird**
2. **Access Add-ons Manager**:
   - Go to `Tools` > `Add-ons and Themes`
   - Or press `Ctrl+Shift+A` (Windows/Linux) or `Cmd+Shift+A` (macOS)

3. **Install the XPI File**:
   - Click the gear icon (⚙️) in the top-right
   - Select `Install Add-on From File...`
   - Navigate to and select `ai-email-reply-assistant-1.0.0.xpi`
   - Click `Open`

4. **Confirm Installation**:
   - Click `Add` when prompted
   - The addon will install and appear in your add-ons list
   - You may need to restart Thunderbird

### Step 3: Verify Installation

1. **Check Toolbar**: Look for the AI Email Reply Assistant button in the toolbar
2. **Test Launch**: Click the button to open the popup interface
3. **Check Status**: You should see "Ready" in the status bar

## API Key Configuration

### Getting DeepSeek API Key

1. **Visit DeepSeek Platform**:
   - Go to [platform.deepseek.com](https://platform.deepseek.com/)
   - Sign up for an account

2. **Generate API Key**:
   - Navigate to API Keys section
   - Click "Create new secret key"
   - Copy the key (starts with `sk-`)
   - Store it securely

3. **Pricing**: Check current pricing at [platform.deepseek.com/pricing](https://platform.deepseek.com/pricing)

### Getting OpenRouter API Key

1. **Visit OpenRouter**:
   - Go to [openrouter.ai](https://openrouter.ai/)
   - Create an account

2. **Generate API Key**:
   - Go to your dashboard
   - Navigate to API Keys
   - Generate a new key
   - Copy the key

3. **Add Credits**: Add credits to your account for API usage

### Configuring API Keys in the Addon

1. **Open the Addon**: Click the AI Email Reply Assistant button
2. **Access Settings**: Click the `⚙️ Settings` button
3. **Enter API Keys**:
   - Paste your DeepSeek API key in the DeepSeek field
   - Paste your OpenRouter API key in the OpenRouter field
   - Choose your default model
4. **Save Configuration**: Click `Save Configuration`
5. **Test**: Try generating a reply to verify the setup

## Troubleshooting Installation

### Common Installation Issues

#### Issue: "This add-on could not be installed"
**Causes**:
- Incompatible Thunderbird version
- Corrupted download
- Insufficient permissions

**Solutions**:
1. Check Thunderbird version (must be 78.0+)
2. Re-download the addon
3. Run Thunderbird as administrator (Windows)
4. Check file permissions

#### Issue: "Add-on appears but button is missing"
**Causes**:
- Toolbar customization needed
- Extension not fully loaded

**Solutions**:
1. Right-click toolbar > Customize
2. Drag the AI Email Reply Assistant button to toolbar
3. Restart Thunderbird

#### Issue: "Permission denied" errors
**Causes**:
- Insufficient file permissions
- Antivirus blocking

**Solutions**:
1. Check file/folder permissions
2. Add Thunderbird to antivirus exceptions
3. Temporarily disable antivirus during installation

### API Configuration Issues

#### Issue: "API key not configured"
**Solutions**:
1. Verify API key is correctly copied (no extra spaces)
2. Check if API key is active on the provider's platform
3. Ensure you have credits/quota available

#### Issue: "Network error"
**Solutions**:
1. Check internet connection
2. Verify firewall settings allow Thunderbird internet access
3. Check if corporate proxy is blocking API calls

#### Issue: "Invalid API key format"
**Solutions**:
1. Verify the API key starts with expected prefix (usually `sk-`)
2. Check for any truncation during copy/paste
3. Generate a new API key if needed

## Advanced Configuration

### Custom Model Settings

You can modify the AI models in the configuration:

```json
{
  "models": {
    "deepseek": {
      "name": "DeepSeek Chat",
      "endpoint": "https://api.deepseek.com/v1/chat/completions",
      "model": "deepseek-chat",
      "enabled": true
    },
    "openrouter": {
      "name": "OpenRouter GPT-4",
      "endpoint": "https://openrouter.ai/api/v1/chat/completions",
      "model": "openai/gpt-4",
      "enabled": true
    }
  }
}
```

### Debugging Mode

To enable detailed logging:

1. Open Thunderbird's Error Console:
   - `Tools` > `Developer Tools` > `Error Console`
2. Look for messages prefixed with "AI Email Reply Assistant"
3. Enable verbose logging by setting debug mode in configuration

### Performance Optimization

For better performance:

1. **Limit Email Count**: Reduce the number of emails fetched
2. **API Timeouts**: Adjust timeout settings for slow connections
3. **Cache Settings**: Enable response caching if available

## Uninstallation

### Complete Removal

1. **Remove Addon**:
   - Go to `Tools` > `Add-ons and Themes`
   - Find "AI Email Reply Assistant"
   - Click `Remove`

2. **Clear Configuration** (Optional):
   - The addon stores configuration in Thunderbird's local storage
   - Configuration is automatically removed when addon is uninstalled

3. **Clean Toolbar**:
   - Right-click toolbar > Customize
   - Remove any remaining buttons

## Security Considerations

### API Key Security
- API keys are stored locally in Thunderbird's secure storage
- Keys are never transmitted except to the respective AI providers
- Consider using API keys with limited permissions/quotas

### Email Privacy
- Email content is only sent to the selected AI provider
- No email content is stored by the addon
- Review AI provider's privacy policies

### Network Security
- All API communications use HTTPS encryption
- Consider using VPN if required by your organization
- Monitor API usage through provider dashboards

## Getting Help

### Documentation
- **README.md**: General overview and features
- **INSTALLATION.md**: This installation guide
- **Inline Comments**: Detailed code documentation

### Support Channels
- **GitHub Issues**: Report bugs and request features
- **GitHub Discussions**: Community support and questions
- **Email**: Contact maintainers directly

### Before Seeking Help
1. Check this installation guide
2. Review the troubleshooting section
3. Check Thunderbird's Error Console for detailed errors
4. Verify API keys and internet connectivity
5. Try with a fresh Thunderbird profile

## Next Steps

After successful installation:

1. **Test Basic Functionality**: Generate a reply for a test email
2. **Explore Features**: Try different AI models and tone matching
3. **Customize Settings**: Adjust preferences to your workflow
4. **Read Documentation**: Review README.md for advanced features
5. **Provide Feedback**: Share your experience and suggestions

---

**Need Help?** If you encounter issues not covered in this guide, please create an issue on GitHub with:
- Your Thunderbird version
- Operating system
- Error messages from the Error Console
- Steps to reproduce the problem
